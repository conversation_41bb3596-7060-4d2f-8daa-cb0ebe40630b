document.addEventListener("DOMContentLoaded", function () {
  const modal = document.getElementById("product-modal");
  const closeBtn = document.getElementById("modal-close");

  document.querySelectorAll(".btn-quick-view").forEach((btn) => {
    btn.addEventListener("click", () => {
      modal.classList.remove("product-modal--hidden");
    });
  });

  closeBtn.addEventListener("click", () => {
    modal.classList.add("product-modal--hidden");
  });

  modal.addEventListener("click", (e) => {
    if (e.target.classList.contains("product-modal__overlay")) {
      modal.classList.add("product-modal--hidden");
    }
  });
});
const quickViewSwiper = new Swiper('.swiper-quick-view', {
    slidesPerView: 1,
    spaceBetween: 8,
  });

  document.querySelectorAll('.product-thumbnails img').forEach((thumb, index) => {
    thumb.addEventListener('click', () => {
      quickViewSwiper.slideTo(index);
      // Toggle active class
      document.querySelectorAll('.product-thumbnails img').forEach(img => img.classList.remove('active'));
      thumb.classList.add('active');
    });
  });

  // Optional: set active on initial load
  document.querySelectorAll('.product-modal__info p').forEach(p => {
    const links = p.querySelectorAll('a');
    links.forEach((link, index) => {
        if (index < links.length - 1) {
            link.insertAdjacentText('afterend', ',');
        }
    });
  });


  // Swiper
  const slideCount = document.querySelectorAll(".swiper-feature-offer .swiper-slide").length;
  const enableLoop = slideCount >= 3;
  var swiper = new Swiper(".swiper-feature-offer", {
      autoplay: {
          delay: 3000,
          disableOnInteraction: false,
      },
      navigation: {
          nextEl: ".collection__next",
          prevEl: ".collection__prev"
      },
      speed: 1200,
      loop: enableLoop,
      breakpoints: {
          0: {
            slidesPerView: 1,
          },
          768: {
              slidesPerView: 3,
          },
      },
  });