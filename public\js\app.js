const appMain = (function () {
  const htmlEl = document.querySelector("html");
  const searchDropdown = document.querySelector(".search__dropdown");
  const btnSearch = document.querySelector(".search-link");

  const dropdownTitle = document.getElementById("search-cate");
  const dropdownList = document.querySelector(".search-category__dropdown");
  let colors = document.querySelectorAll(".product-color");
  const maxColorVisible = 6;

  const headerSticky = () => {
    const body = document.body;
    const headerContainer = document.querySelector(".header-wrapper");
    const headerTop = document.querySelector(".header-info-bar");
    const topHeaderHeight = headerTop.offsetHeight || 0;
    const targetScroll = topHeaderHeight + 200;

    window.addEventListener("scroll", function () {
      if (!body.classList.contains("maybi-header-sticky")) return; // Early exit for efficiency
      if (window.scrollY > targetScroll) {
        headerContainer.classList.add("maybi-sticky");
      } else {
        headerContainer.classList.remove("maybi-sticky");
      }
    });
  };

  const searchCategoryDropdown = () => {
    if (!dropdownTitle || !dropdownList) return;

    // Toggle dropdown when title clicked
    dropdownTitle.addEventListener("click", () => {
      dropdownList.classList.toggle("show");
    });

    // Handle click on each dropdown item
    const items = dropdownList.querySelectorAll("li[data-value]");
    items.forEach((item) => {
      item.addEventListener("click", () => {
        const selectedText = item.textContent.trim();
        const selectedValue = item.getAttribute("data-value");

        // Update dropdown title
        dropdownTitle.textContent = selectedText;
        dropdownTitle.setAttribute("data-value", selectedValue);

        // Update active state
        items.forEach((i) => i.classList.remove("active"));
        item.classList.add("active");
        // Close dropdown
        dropdownList.classList.remove("show");
      });
    });

    // Close dropdown if clicked outside
    document.addEventListener("click", (e) => {
      if (
        !dropdownTitle.contains(e.target) &&
        !dropdownList.contains(e.target)
      ) {
        dropdownList.classList.remove("show");
      }
    });
  };

  const btnSearchClick = () => {
    if (!btnSearch) return;
    btnSearch.addEventListener("click", function (e) {
      e.preventDefault();
      htmlEl.classList.toggle("search-opened");
      this.classList.toggle("open");
      searchDropdown.classList.toggle("active");
    });
  };

  const closeSearchDropdown = () => {
    document.addEventListener("click", function (e) {
      if (htmlEl.classList.contains("search-opened")) {
        closeSideNav(e);
      }
    });
  };

  const closeSideNav = function (e) {
    if (!searchDropdown.contains(e.target) && !btnSearch.contains(e.target)) {
      htmlEl.classList.toggle("search-opened");
      btnSearch.classList.toggle("open");
      searchDropdown.classList.toggle("active");
    }
  };

  const showColor = () => {
    colors.forEach((color) => {
      color.addEventListener("click", (e) => {
        let productId = color.getAttribute("data-id");
        let pathImg = color.getAttribute("data-img");
        if (!productId || !pathImg) return;

        const parentProduct = color.closest(".product");
        const siblingColors = parentProduct.querySelectorAll(".product-color");
        siblingColors.forEach((i) => i.classList.remove("active"));

        color.classList.add("active");
        parentProduct
          .querySelector("#" + productId)
          .setAttribute("src", pathImg);
      });
    });
  };

  const changePruductColor = () => {
    const colorButtons = document.querySelectorAll(".product-color");

    colorButtons.forEach(color => {
      if (color.classList.contains('product-color--more')) return;
      color.addEventListener('click', () => {
        const img = color.getAttribute('data-img');
        const product = color.closest('.product');
        const mainImg = product.querySelector('.product__img');

        const allColors = product.querySelectorAll(".product-color");
        allColors.forEach((c) => c.classList.remove("active"));
        color.classList.add("active");

        if (mainImg) {
          mainImg.setAttribute("src", img);
        }
      });
    });
  };

  const changePruductSize = () => {
    const sizes = document.querySelectorAll(".product-size__item");

    sizes.forEach((item) => {
      item.addEventListener("click", (el) => {
        if (!el.target.dataset.color) return;
        const colorItems = JSON.parse(el.target.dataset.color);
        const productWrapper = el.currentTarget.closest(".product");
        const productInfo = productWrapper.querySelector(".product__info");
        const productColors = productInfo.querySelector(".product-colors");

        const currentActiveColor = productColors.querySelector(
          ".product-color.active"
        );
        const currentImg = currentActiveColor?.getAttribute("data-img");

        let colorHTML = "";
        const visibleColors = colorItems.slice(0, maxColorVisible);
        const remaining = colorItems.length - maxColorVisible;

        visibleColors.forEach((colorItem) => {
          colorHTML += `
              <li class="product-color" data-img="${colorItem.img}">
                <span class="product-color__item" style="background-color: ${colorItem.color};"></span>
              </li>`;
        });

        if (remaining > 0) {
          colorHTML += `
              <li class="product-color product-color--more">
                <span>+${remaining}</span>
              </li>`;
        }

        productColors.innerHTML = colorHTML;

        const newColorElements =
          productColors.querySelectorAll(".product-color");
        let foundMatch = false;

        newColorElements.forEach((colorEl) => {
          if (colorEl.getAttribute("data-img") === currentImg) {
            colorEl.classList.add("active");
            foundMatch = true;
          }
        });

        if (!foundMatch && newColorElements.length > 0) {
          newColorElements[0].classList.add("active");
        }

        const activeColor = productColors.querySelector(
          ".product-color.active"
        );
        const newImg = activeColor?.getAttribute("data-img");
        const mainImg = productWrapper.querySelector(".product__img");
        if (mainImg && newImg) {
          mainImg.setAttribute("src", newImg);
        }

        const activeSize = productWrapper.querySelector(
          ".product-size--active"
        );
        if (activeSize) {
          activeSize.classList.remove("product-size--active");
        }
        el.currentTarget.classList.add("product-size--active");

        changePruductColor();
      });
    });
  };

  const btnMenuMobileBar = () => {
    const menuMobileBar = document.querySelector(".menu-mobile-bar");
    const menusMobile = document.querySelector(".menus-mobile");  
    if (!menuMobileBar) return;
    menuMobileBar.addEventListener("click", function () {
      this.classList.toggle("open");
      menusMobile.classList.toggle("show");      
      if (menusMobile.classList.contains("show")) {
        document.body.classList.add("no-scroll");
      } else {
        document.body.classList.remove("no-scroll");
      }
    });
  };

  const bindQuantityControl = () => {
    document
      .querySelectorAll(".quantity-control")
      .forEach(function (control) {
        const input = control.querySelector(".quantity-input");
        const btnUp = control.querySelector(".quantity__btn-up");
        const btnDown = control.querySelector(".quantity__btn-down");

        btnUp.addEventListener("click", function () {
          let value = parseInt(input.value, 10) || 0;
          input.value = value + 1;
        });

        btnDown.addEventListener("click", function () {
          let value = parseInt(input.value, 10) || 0;
          if (value > 1) {
            input.value = value - 1;
          }
        });
      });
  };

  const closeOffcanvasCart= () => {

    const cartSidebar = document.getElementById("cartSidebar");
    const cartPanel = cartSidebar.querySelector(".sheet__panel");
    const closeBtn = document.querySelector(".sheet__close");

    closeBtn.addEventListener("click", () => {
      cartSidebar.classList.remove("is-open");
    });

    cartSidebar.addEventListener("click", (e) => {
      if (!cartPanel.contains(e.target)) {
        cartSidebar.classList.remove("is-open");
      }
    });
  };

  return {
    init: function () {
      headerSticky();
      searchCategoryDropdown();
      btnSearchClick();
      closeSearchDropdown();
      showColor();
      changePruductColor();
      changePruductSize();
      btnMenuMobileBar();
      bindQuantityControl();
      closeOffcanvasCart();
    },
  };
})();

document.addEventListener("DOMContentLoaded", function (event) {
  appMain.init();
});