.footer {
  font-size: 0.652vw;
  padding-top: 6em;
  background-color: rgba(var(--rgb-black),0.05);
}

.footer__wrapper {
  display: grid;
  grid-template-columns: repeat(5, auto);
  gap: 2em;
}

.footer__col {
  min-width: 15em;
}



.footer h5,
.footer h6 {
  font-size: 2em;
  margin-bottom: 1.2em;
  text-transform: uppercase;
}
.footer-logo{
  margin-bottom: 2.4em;
}
.footer-logo img {
  width: 15em;
  
}
.footer__address {
  list-style: none;
  padding: 0;
  margin: 0 0 1.6em 0;
  font-size: 1.3em;
}
.footer__address li span {
  font-weight: bold;
}


.recent-posts > li {
  display: flex;
  gap: 1em; 
  margin-bottom: 1.2em;
}


.recent-posts > li > img {
  width: 6em;
  height: 6em;
  object-fit: cover;
  border-radius: 1em;
}

.recent-posts > li > .recent-post__title {
    display: flex;
    flex-direction: column;
    gap: .5em;

}

.recent-posts > li > .recent-post__title > a {
  font-size: 1.5em;
  font-weight: 600;
  width: 100%;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  white-space: initial;
  overflow: hidden;
}




.footer__subscribe h6 {
  margin-bottom: 0.5em;
}

.footer__subscribe form {
  display: flex;
  margin-top: 1em;
  position: relative;
  min-height: 3em;
  overflow: hidden;
}
.footer__subscribe input[type="email"] {
  flex: 1;
  padding: 0 2em; 
  border: none; 
  background-color: #FEEB9D;
  height: 3em;
  border-radius: 1em;
  color: var(--black);
  position: relative;
  font-size: 1.4em;

}

.footer__subscribe input[type="email"]::placeholder {
  color: var(--black);
}

.footer__subscribe button {
  color: var(--black);
  border: none;
  cursor: pointer;
  position: absolute;
  background-color: transparent;
  right: 0.5em;
  top: 0.4em;
  font-size: 2em;
}




.footer__subscribe button > i {
  display: inline-block;
  transition: none;
}

.footer__subscribe button:hover > i {
  animation: slideOutInIcon 0.6s ease forwards;
}

@keyframes slideOutInIcon {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  40% {
    transform: translateX(2em);
    opacity: 0;
  }
  60% {
    transform: translateX(-1em);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}



.footer__col > ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer__col > ul.footer__address {
  display: flex;
  flex-direction: column;
  gap: 0.8em;
  margin-bottom: 2.4em;
  font-size: 1.5em;

}
.footer__col ul.footer__address > li{
  margin-bottom: 0;
}
.footer__col ul > li {
  margin-bottom: 1.5em;
}


.footer__col ul > li > a {
  color: var(--black);
  text-decoration: none;
  font-size: 1.5em;
  line-height: 1.2em;
}
.footer__col ul > li > a:hover {
  color: var(--primary);
  transition: all 0.5s ease 0s;
}


.footer__bottom {
    display: flex;
    justify-content: space-between;
    margin-top: 2em;
    padding: 2.8em 0;
    border-top: .1em solid rgba(0, 0, 0, 0.1);
}

.footer__payment > p > span {
    font-weight: bold;
    margin-right: 1em;
    font-size: 1.5em;
}

.footer__bottom img {
    width: 22.3em;
}

.footer__copyright > p {
    font-size: 1.5em;
    font-weight: 400;
}



