package layouts

import (
	"github.com/networld-solution/gos/templates"
    "goweb/views/partials"
    "goweb/frontend/entity"
)

templ Master(seo *entity.Seo, head *[]templ.Component, footers ...templ.Component) {
    <!doctype html>
    <html lang="en" data-layout="vertical" data-topbar="light" data-sidebar="dark" data-sidebar-size="sm-hover" data-sidebar-image="none" data-preloader="disable">
        <head>
            <meta charset="UTF-8">
            <title>MAYBI Womenswear - Thương hiệu thời trang nữ thiết kế</title>
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <link rel="shortcut icon" href={templates.AssetURL("/static/images/fav.png")} type="image/x-icon">
            <link rel="stylesheet" href={templates.AssetURL("/static/css/default.css")}>
            <link rel="stylesheet" href={templates.AssetURL("/static/css/icons.min.css")}>
            <link rel="stylesheet" href={templates.AssetURL("/static/css/maybi-ui.css")}>
            <link rel="stylesheet" href={templates.AssetURL("/static/css/style.css")}>
            <link rel="stylesheet" href={templates.AssetURL("/static/css/menus.css")}>
            <link rel="stylesheet" href={templates.AssetURL("/static/css/menus-rep.css")}>
            <link rel="stylesheet" href={templates.AssetURL("/static/css/footer.css")}>  
            if head != nil && len(*head) > 0 {
                for _, item := range *head{
                    @item
                }
            }
            <link rel="stylesheet" href={templates.AssetURL("/static/css/reponsive.css")}>
        </head>
        <body class="maybi-header-sticky">
            @Header()
            { children... }
            @Footer()
            @partials.SvgSymbols() 
            <script type="text/javascript" src={templates.AssetURL("/static/js/app.js")}></script>
            if len(footers) > 0 {
                for _, footer := range footers {
                    @footer
                }
            }
        </body>
    </html>
}