package home

import (
    "goweb/views/layouts"
    "goweb/views/home/<USER>"
    "github.com/networld-solution/gos/templates"
    "goweb/views/partials"
)

templ Index() {
    @layouts.Master(nil, &[]templ.Component{head()}, scriptSlot()) { 
        @components.SlideshowHome()       
        @components.VouchersHomeCpn()
        @components.FlashsaleHome()
        @components.CategoryHome()   
        @components.NewArrivals()
        @components.VideoHome()
        @components.FeaturedOfferForYou()
        @components.BestSellerHome()
        @partials.OffcanvasCart()
        @components.QuickViewModal()
        @components.PromotionModal()
    }
}

templ head(){
	<link rel="stylesheet" href={templates.AssetURL("/static/css/quick_view_modal.css")}>
	<link rel="stylesheet" href={templates.AssetURL("/static/css/featured_offer.css")}>
	<link rel="stylesheet" href={templates.AssetURL("/static/css/category.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/static/css/promotion-modal.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/static/css/flashsale.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/static/css/slideshow.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/static/libs/swiper/swiper-bundle.min.css")} />
	<link rel="stylesheet" href={templates.AssetURL("/static/css/video.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/static/css/mega-voucher.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/static/css/offcanvas_cart.css")}>
}

templ scriptSlot(){
    <script type="text/javascript" src={templates.AssetURL("/static/js/maybi-ui.js")}></script>
    <script type="text/javascript" src={templates.AssetURL("/static/libs/swiper/swiper-bundle.min.js")}></script>
    <script>
        document.addEventListener("DOMContentLoaded", function (event) {
            maybiUI.flashSale();
        });

        function calcSpaceBetweenFromVW(em) {
            const fs = (window.innerWidth * 0.652) / 100;
            if (fs >20) return 20;
            return em * fs;
        }

        var swiper = new Swiper(".flashsale__swiper", {
            loop: true,
            autoplay: {
                delay: 3000,
                disableOnInteraction: true,
            },
            navigation: {
                nextEl: ".flashsale__next",
                prevEl: ".flashsale__prev"
            },   
            spaceBetween: calcSpaceBetweenFromVW(1.5),         
            breakpoints: {
                320: {
                    slidesPerView: 2,
                    spaceBetween: calcSpaceBetweenFromVW(2),
                },
                768: {
                    slidesPerView: 5,
                },
            },
        });
        
        var swiper = new Swiper(".swiper-quick-view", {
            loop: true,
            spaceBetween: 8,
            breakpoints: {
                768: {
                    slidesPerView: 1,
                },
            },
        });
        var banner_slides = document.getElementsByClassName("banner-slides");
        if (banner_slides.length > 0) {
            var swiper = new Swiper(".banner-slides", {
                loop: true,
                slidesPerView: 1,
                autoplay: {
                    delay: 3000,
                    disableOnInteraction: true,
                },
                pagination: {
                el: ".swiper-pagination",
                    clickable: true,
                },
            });
        }
    </script>
    <script src="https://www.youtube.com/iframe_api"></script>
    <script src={templates.AssetURL("/static/js/customize_youtube_video.js")}></script>
    <script src={templates.AssetURL("/static/js/customize_quick_view_modal.js")}></script>
    <script src={templates.AssetURL("/static/js/cart_updater.js")}></script>
}