const maxColorVisible = 6;

const cartUpdater = (function () {
  let wrapper;
  const bindQuantityButtons = () => {
    const minusBtns = wrapper.querySelectorAll(".quantity-cart__btn-down");
    const plusBtns = wrapper.querySelectorAll(".quantity-cart__btn-up");
    const quantityInputs = wrapper.querySelectorAll(".quantity-cart__input");

    minusBtns.forEach((btn) =>
      btn.addEventListener("click", () => changeQuantity(btn, -1))
    );

    plusBtns.forEach((btn) =>
      btn.addEventListener("click", () => changeQuantity(btn, 1))
    );

    quantityInputs.forEach((input) => {
      input.addEventListener("input", () => validateAndUpdateInput(input));
      input.addEventListener("change", () => validateAndUpdateInput(input));
    });
  }

  const bindDeleteButtons = () => {
    const deleteBtns = wrapper.querySelectorAll(".cart__item__delete");
    deleteBtns.forEach((btn) => {
      btn.addEventListener("click", () => {
        const item = btn.closest(".cart__item");
        if (item) {
          item.remove();
          updateCartSummary();
          syncCartToLocalStorage();
        }
      });
    });
  }

  const updateCartSummary = () => {
    const items = wrapper.querySelectorAll(".cart__item");
    let total = 0;

    items.forEach((item) => {
      const priceText = item.querySelector(".cart__item__price--sale")?.textContent || "0";
      const input = item.querySelector(".quantity-cart__input") ||
        item.querySelector(".cart__item__quantity--number span");
      const quantity = parseInt(input?.value || input?.textContent || "1", 10);
      const price = parseCurrency(priceText);
      total += quantity * price;
    });

    renderTotalPrice(total);
    updateFreeshipBar(total);
    updateProductCount();

    const freeshipBox = wrapper.querySelector(".freeship_progress");
    if (freeshipBox) {
      freeshipBox.setAttribute("data-current", total);
    }
  }

  function changeQuantity(btn, delta) {
    const input = btn.closest(".quantity-cart")?.querySelector(".quantity-cart__input");
    if (!input) return;

    let quantity = parseInt(input.value || "1", 10);
    quantity = Math.max(1, quantity + delta);
    input.value = quantity;

    updateCartSummary();
    syncCartToLocalStorage();
  }

  function validateAndUpdateInput(input) {
    let value = parseInt(input.value || "1", 10);
    if (isNaN(value) || value < 1) value = 1;
    input.value = value;

    updateCartSummary();
    syncCartToLocalStorage();
  }

  function renderTotalPrice(total) {
    const totalEl = wrapper.querySelector(".cart__total__price");
    if (totalEl) {
      totalEl.textContent = formatCurrency(total);
    }
  }

  function updateFreeshipBar(current) {
    const freeshipBox = wrapper.querySelector(".freeship_progress");
    if (!freeshipBox) return;

    const target = parseInt(freeshipBox.dataset.target || "0", 10);
    const needMsg = freeshipBox.querySelector(".freeship-message-need");
    const successMsg = freeshipBox.querySelector(".freeship-message-success");
    const valueBar = freeshipBox.querySelector(".freeship_progress__bar-value");
    const amountText = freeshipBox.querySelector(".freeship-icon__amount");
    const endText = freeshipBox.querySelector(".freeship_progress__bar-end");

    const progress = Math.min((current / target) * 100, 100);
    if (valueBar) valueBar.style.width = `${progress}%`;
    if (amountText) amountText.textContent = formatCurrency(current);
    if (endText) endText.textContent = formatCurrency(target);

    if (current >= target) {
      successMsg.style.display = "inline-block";
      needMsg.style.display = "none";
    } else {
      successMsg.style.display = "none";
      const remaining = target - current;
      const bold = needMsg.querySelector("b");
      if (bold) {
        bold.textContent = formatCurrency(remaining);
      }

      needMsg.style.display = "block";
    }
  }

  function updateProductCount() {
    const countEl = wrapper.querySelector(".cart__total__count");
    const items = wrapper.querySelectorAll(".cart__item");
    let totalQuantity = 0;

    items.forEach((item) => {
      const input = item.querySelector(".quantity-cart__input") ||
        item.querySelector(".cart__item__quantity--number span");
      const quantity = parseInt(input?.value || input?.textContent || "1", 10);
      totalQuantity += quantity;
    });

    if (countEl) {
      countEl.textContent = totalQuantity;
    }
  }

  return {
    init(selector) {
      wrapper = document.querySelector(selector);
      if (!wrapper) return false;
      renderInitCart();
      changePruductColor();
      changePruductSize();
      triggerCartOffcanvas();
      bindQuantityButtons();
      bindDeleteButtons();
      updateCartSummary();
      return true;
    },
    rebind() {
      bindQuantityButtons();
      bindDeleteButtons();
      updateCartSummary();
    }
  };

})();

document.addEventListener("DOMContentLoaded", () => {
  cartUpdater.init("#cartSidebar");
});


function formatCurrency(number) {
  return number.toLocaleString("vi-VN") + "đ";
}

function parseCurrency(text) {
  return parseInt(text.replace(/[^\d]/g, ""), 10) || 0;
}


const renderInitCart = () => {
  const cartList = document.querySelector("#cartSidebar .cart__list");
  if (!cartList) return;

  const cartItems = JSON.parse(localStorage.getItem("cartItems") || "[]");
  if (cartItems.length === 0) return;
  cartList.innerHTML = "";

  cartItems.forEach(item => {
    const sizeHTML = item.sizes.map(size => `
      <li class="product-size__item maybi-hover ${size.isActive ? "product-size--active" : ""}"
          data-color='${size.dataColor || ""}'>${size.text}</li>
    `).join("");

    const colorHTML = item.colors.map(color => `
      <li class="product-color ${color.isActive ? "active" : ""}"
          data-img="${color.dataImg}">
        <span class="product-color__item" style="background-color: ${color.backgroundColor};"></span>
      </li>
    `).join("");

    const html = `
      <div class="cart__item product">
        <div class="cart__item__img">
          <img class="product__img" src="${item.image}" alt="${item.name}" />
        </div>
        <div class="cart__item__content product__info">
          <h3 class="cart__item__title">
            <a href="#" title="${item.name}">${item.name}</a>
          </h3>
          <div class="cart__item__info">
            <div class="quantity-cart">
              <button class="quantity-cart__btn quantity-cart__btn-down" type="button">
                <svg class="product-icon__down">
                    <use href="#icon-down"></use>
                </svg>
              </button>
              <input class="quantity-cart__input" type="text" value="${item.quantity}" />
              <button class="quantity-cart__btn quantity-cart__btn-up" type="button">
                <svg class="product-icon__up">
                    <use href="#icon-up"></use>
                </svg>
              </button>
            </div>
            <div class="cart__item__price">
              <span class="cart__item__price--sale">${item.price || "--"}</span>
            </div>
          </div>
          <div class="cart__item__variants">
            <span class="text-muted">Size:</span>
            <ul class="product-size">${sizeHTML}</ul>
          </div>
          <div class="cart__item__variants">
            <span class="text-muted">Màu:</span>
            <ul class="product-colors">${colorHTML}</ul>
          </div>
        </div>
        <div class="cart__item__delete">
          <svg class="cart-icon__delete"><use href="#icon-close"></use></svg>
        </div>
      </div>
    `;
    cartList.insertAdjacentHTML("beforeend", html);
  });
};


const changePruductColor = () => {
  // Gán 1 lần cho cha chứa tất cả màu
  document.querySelector("#cartSidebar").addEventListener("click", (e) => {
    const color = e.target.closest(".product-color");
    if (!color || color.classList.contains("product-color--more")) return;

    const img = color.getAttribute("data-img");
    const product = color.closest(".product");
    const mainImg = product.querySelector(".product__img");

    const allColors = product.querySelectorAll(".product-color");
    allColors.forEach((c) => c.classList.remove("active"));
    color.classList.add("active");

    if (mainImg && img) {
      mainImg.setAttribute("src", img);
    }

    syncCartToLocalStorage();
  });
};


const changePruductSize = () => {
  const sizes = document.querySelectorAll(".product-size__item");

  sizes.forEach((item) => {
    item.addEventListener("click", (el) => {
      if (!el.target.dataset.color) return;
      const colorItems = JSON.parse(el.target.dataset.color);
      const productWrapper = el.currentTarget.closest(".product");
      const productInfo = productWrapper.querySelector(".product__info");
      const productColors = productInfo.querySelector(".product-colors");

      const currentActiveColor = productColors.querySelector(
        ".product-color.active"
      );
      const currentImg = currentActiveColor?.getAttribute("data-img");

      let colorHTML = "";
      const visibleColors = colorItems.slice(0, maxColorVisible);
      const remaining = colorItems.length - maxColorVisible;

      visibleColors.forEach((colorItem) => {
        colorHTML += `
              <li class="product-color" data-img="${colorItem.img}">
                <span class="product-color__item" style="background-color: ${colorItem.color};"></span>
              </li>`;
      });

      if (remaining > 0) {
        colorHTML += `
              <li class="product-color product-color--more">
                <span>+${remaining}</span>
              </li>`;
      }

      productColors.innerHTML = colorHTML;

      const newColorElements =
        productColors.querySelectorAll(".product-color");
      let foundMatch = false;

      newColorElements.forEach((colorEl) => {
        if (colorEl.getAttribute("data-img") === currentImg) {
          colorEl.classList.add("active");
          foundMatch = true;
        }
      });

      if (!foundMatch && newColorElements.length > 0) {
        newColorElements[0].classList.add("active");
      }

      const activeColor = productColors.querySelector(
        ".product-color.active"
      );
      const newImg = activeColor?.getAttribute("data-img");
      const mainImg = productWrapper.querySelector(".product__img");
      if (mainImg && newImg) {
        mainImg.setAttribute("src", newImg);
      }

      const activeSize = productWrapper.querySelector(
        ".product-size--active"
      );
      if (activeSize) {
        activeSize.classList.remove("product-size--active");
      }
      el.currentTarget.classList.add("product-size--active");

      changePruductColor();
      syncCartToLocalStorage();
    });
  });
};


const triggerCartOffcanvas = () => {
  document.addEventListener("click", function (e) {
    const btn = e.target.closest(".btn_open_cart");
    if (!btn) return;

    const productEl = btn.closest(".product");
    if (!productEl) return;

    // 1. Thông tin chính
    const name = productEl.querySelector(".product__title a")?.textContent.trim() || "";
    const productId = btn.dataset.productId || "";
    const image = productEl.querySelector(".product__img")?.getAttribute("src") || "";
    const price = productEl.querySelector(".sale-price")?.textContent.trim() || "";

    // 2. Danh sách size (có thể chứa `data-color`)
    const sizeElements = productEl.querySelectorAll(".product-size__item");
    const sizes = Array.from(sizeElements).map((el) => ({
      text: el.textContent.trim(),
      isActive: el.classList.contains("product-size--active"),
      dataColor: el.getAttribute("data-color") || null,
    }));

    // 3. Danh sách màu
    const colorElements = productEl.querySelectorAll(".product-color");
    const colors = Array.from(colorElements)
      .filter((el) => !el.classList.contains("product-color--more"))
      .map((el) => ({
        backgroundColor: el.querySelector("span")?.style.backgroundColor || "",
        dataImg: el.getAttribute("data-img") || "",
        isActive: el.classList.contains("active"),
      }));

    // 4. Số lượng
    const quantityInput = productEl.querySelector(".quantity-input");
    const quantity = quantityInput ? parseInt(quantityInput.value || "1", 10) : 1;

    const productData = {
      product_id: productId,
      name,
      image,
      sizes,
      colors,
      quantity,
      price
    };

    addToCartLocalstorage(productData)
    addToCart(productData);
    cartUpdater.rebind();

    const cartSidebar = document.getElementById("cartSidebar");
    cartSidebar?.classList.add("is-open");
  });
};

function addToCartLocalstorage(item) {
  // Step 1: Get existing data
  const existingCart = JSON.parse(localStorage.getItem("cartItems") || "[]");

  // Step 2: Add new item
  existingCart.push(item);

  // Step 3: Overwrite localStorage
  localStorage.setItem("cartItems", JSON.stringify(existingCart));
}


const addToCart = (productData) => {
  const cartList = document.querySelector("#cartSidebar .cart__list");
  if (!cartList) return;

  // Render size
  const sizeHTML = productData.sizes.map((size) => `
    <li class="product-size__item maybi-hover ${size.isActive ? 'product-size--active' : ''}"
        data-color='${size.dataColor || ""}'>${size.text}</li>
  `).join("");

  // Render color
  const colorHTML = productData.colors.map((color) => `
    <li class="product-color ${color.isActive ? 'active' : ''}" data-img="${color.dataImg}">
      <span class="product-color__item" style="background-color: ${color.backgroundColor};"></span>
    </li>
  `).join("");

  const html = `
    <div class="cart__item product">
      <div class="cart__item__img">
        <img class="product__img" src="${productData.image}" alt="${productData.name}" />
      </div>
      <div class="cart__item__content product__info">
        <h3 class="cart__item__title">
          <a href="#" title="${productData.name}">${productData.name}</a>
        </h3>
        <div class="cart__item__info">
          <div class="quantity-cart">
            <button class="quantity-cart__btn quantity-cart__btn-down" type="button">
              <svg class="product-icon__down"><use href="#icon-down"></use></svg>
            </button>
            <input class="quantity-cart__input" type="text" value="${productData.quantity}" />
            <button class="quantity-cart__btn quantity-cart__btn-up" type="button">
              <svg class="product-icon__up"><use href="#icon-up"></use></svg>
            </button>
          </div>
          <div class="cart__item__price">
            <span class="cart__item__price--sale">${productData.price}</span>
          </div>
        </div>
        <div class="cart__item__variants">
          <span class="text-muted">Size:</span>
          <ul class="product-size">${sizeHTML}</ul>
        </div>
        <div class="cart__item__variants">
          <span class="text-muted">Màu:</span>
          <ul class="product-colors">${colorHTML}</ul>
        </div>
      </div>
      <div class="cart__item__delete">
        <svg class="cart-icon__delete"><use href="#icon-close"></use></svg>
      </div>
    </div>
  `;

  cartList.insertAdjacentHTML("beforeend", html);
  changePruductColor();
  changePruductSize();
};

function syncCartToLocalStorage() {
  const items = document.querySelectorAll("#cartSidebar .cart__item");
  const updatedCart = [];

  items.forEach((item) => {
    const name = item.querySelector(".cart__item__title a")?.textContent.trim();
    const image = item.querySelector(".product__img")?.getAttribute("src");
    const price = item.querySelector(".cart__item__price--sale")?.textContent.trim();
    const quantity = parseInt(item.querySelector(".quantity-cart__input")?.value || "1", 10);


    // Size
    const sizes = Array.from(item.querySelectorAll(".product-size__item")).map((el) => ({
      text: el.textContent.trim(),
      isActive: el.classList.contains("product-size--active"),
      dataColor: el.getAttribute("data-color") || null
    }));


    // Color
    const colors = Array.from(item.querySelectorAll(".product-color")).map((el) => ({
      backgroundColor: el.querySelector("span")?.style.backgroundColor || "",
      dataImg: el.getAttribute("data-img") || "",
      isActive: el.classList.contains("active")
    }));

    updatedCart.push({
      name,
      image,
      price,
      quantity,
      sizes,
      colors
    });
  });

  localStorage.setItem("cartItems", JSON.stringify(updatedCart));
}

