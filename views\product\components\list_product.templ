package components

import (
"goweb/views/partials"
)

templ ListProduct(){
<section class="list-product">
    <div class="container90">
        <div class="list-product__layout">
            <!-- Sidebar Filter -->
            <div class="list-product__sidebar">
                <div class="list-product__sidebar-sticky">
                    <div class="list-product__filter">
                        <aside>
                            <!-- Filter Header -->
                            <div class="list-product__filter-header">
                                <h6 class="list-product__filter-title">
                                    <div class=" list-product__filter-icon">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <path
                                                d="M6.17071 18C6.58254 16.8348 7.69378 16 9 16C10.3062 16 11.4175 16.8348 11.8293 18H22V20H11.8293C11.4175 21.1652 10.3062 22 9 22C7.69378 22 6.58254 21.1652 6.17071 20H2V18H6.17071ZM12.1707 11C12.5825 9.83481 13.6938 9 15 9C16.3062 9 17.4175 9.83481 17.8293 11H22V13H17.8293C17.4175 14.1652 16.3062 15 15 15C13.6938 15 12.5825 14.1652 12.1707 13H2V11H12.1707ZM6.17071 4C6.58254 2.83481 7.69378 2 9 2C10.3062 2 11.4175 2.83481 11.8293 4H22V6H11.8293C11.4175 7.16519 10.3062 8 9 8C7.69378 8 6.58254 7.16519 6.17071 6H2V4H6.17071ZM9 6C9.55228 6 10 5.55228 10 5C10 4.44772 9.55228 4 9 4C8.44772 4 8 4.44772 8 5C8 5.55228 8.44772 6 9 6ZM15 13C15.5523 13 16 12.5523 16 12C16 11.4477 15.5523 11 15 11C14.4477 11 14 11.4477 14 12C14 12.5523 14.4477 13 15 13ZM9 20C9.55228 20 10 19.5523 10 19C10 18.4477 9.55228 18 9 18C8.44772 18 8 18.4477 8 19C8 19.5523 8.44772 20 9 20Z">
                                            </path>
                                        </svg>
                                    </div>
                                    <span>Filter</span>
                                </h6>
                            </div>

                            <!-- Search Widget -->
                            <div class="list-product__widget list-product__widget--search">
                                <div class="list-product__search-form">
                                    <div class="list-product__input-group">
                                        <input required="" type="search" class="list-product__search-input"
                                            placeholder="Search Product" name="dzSearch">
                                        <div class="list-product__input-addon">
                                            <button value="Submit" type="submit" class="list-product__search-btn"
                                                name="submit">
                                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M9.16667 15.8333C12.8486 15.8333 15.8333 12.8486 15.8333 9.16667C15.8333 5.48477 12.8486 2.5 9.16667 2.5C5.48477 2.5 2.5 5.48477 2.5 9.16667C2.5 12.8486 5.48477 15.8333 9.16667 15.8333Z"
                                                        stroke="#cc0d39cc" stroke-width="2" stroke-linecap="round"
                                                        stroke-linejoin="round"></path>
                                                    <path d="M17.5 17.5L13.875 13.875" stroke="#cc0d39cc"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    </path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Price Widget -->
                            <div class="list-product__widget">
                                <h6 class="list-product__widget-title">Price</h6>
                                <div class="list-product__price-slider">
                                    <div class="list-product__range-slider">
                                        <div id="slider-tooltips2" class="list-product__slider">
                                            <div class="noUi-target noUi-ltr noUi-horizontal noUi-txt-dir-ltr">
                                                <div class="noUi-base">
                                                    <div class="noUi-connects">
                                                        <div class="noUi-connect"
                                                            style="transform: translate(10%, 0px) scale(0.775, 1);">
                                                        </div>
                                                    </div>
                                                    <div class="noUi-origin"
                                                        style="transform: translate(-900%, 0px); z-index: 5;">
                                                        <div class="noUi-handle noUi-handle-lower" data-handle="0"
                                                            tabindex="0" role="slider" aria-orientation="horizontal"
                                                            aria-valuemin="0.0" aria-valuemax="350.0"
                                                            aria-valuenow="40.0" aria-valuetext="$40">
                                                            <div class="noUi-touch-area"></div>
                                                        </div>
                                                    </div>
                                                    <div class="noUi-origin"
                                                        style="transform: translate(-125%, 0px); z-index: 4;">
                                                        <div class="noUi-handle noUi-handle-upper" data-handle="1"
                                                            tabindex="0" role="slider" aria-orientation="horizontal"
                                                            aria-valuemin="40.0" aria-valuemax="400.0"
                                                            aria-valuenow="350.0" aria-valuetext="$350">
                                                            <div class="noUi-touch-area"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="list-product__price-values">
                                            <span class="list-product__price-value" id="slider-margin-value-min2">Min
                                                Price: $50</span>
                                            <span class="list-product__price-value" id="slider-margin-value-max2">Max
                                                Price: $353</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Color Widget -->
                            <div class="list-product__widget">
                                <h6 class="list-product__widget-title">Color</h6>
                                <div class="list-product__color-filter">
                                    <div class="list-product__color-option">
                                        <input class="list-product__color-input" type="radio" id="radioNoLabel01"
                                            name="radioNoLabel" checked="" value="#000000">
                                        <span class="list-product__color-swatch"
                                            style="background-color:#000000"></span>
                                    </div>
                                    <div class="list-product__color-option">
                                        <input class="list-product__color-input" type="radio" id="radioNoLabel02"
                                            name="radioNoLabel" value="#9BD1FF">
                                        <span class="list-product__color-swatch"
                                            style="background-color:#9BD1FF"></span>
                                    </div>
                                    <div class="list-product__color-option">
                                        <input class="list-product__color-input" type="radio" id="radioNoLabel03"
                                            name="radioNoLabel" value="#21B290">
                                        <span class="list-product__color-swatch"
                                            style="background-color:#21B290"></span>
                                    </div>
                                    <div class="list-product__color-option">
                                        <input class="list-product__color-input" type="radio" id="radioNoLabel04"
                                            name="radioNoLabel" value="#FEC4C4">
                                        <span class="list-product__color-swatch"
                                            style="background-color:#FEC4C4"></span>
                                    </div>
                                    <div class="list-product__color-option">
                                        <input class="list-product__color-input" type="radio" id="radioNoLabel05"
                                            name="radioNoLabel" value="#FF7354">
                                        <span class="list-product__color-swatch"
                                            style="background-color:#FF7354"></span>
                                    </div>
                                    <div class="list-product__color-option">
                                        <input class="list-product__color-input" type="radio" id="radioNoLabel06"
                                            name="radioNoLabel" value="#51EDC8">
                                        <span class="list-product__color-swatch"
                                            style="background-color:#51EDC8"></span>
                                    </div>
                                    <div class="list-product__color-option">
                                        <input class="list-product__color-input" type="radio" id="radioNoLabel07"
                                            name="radioNoLabel" value="#B77CF3">
                                        <span class="list-product__color-swatch"
                                            style="background-color:#B77CF3"></span>
                                    </div>
                                    <div class="list-product__color-option">
                                        <input class="list-product__color-input" type="radio" id="radioNoLabel08"
                                            name="radioNoLabel" value="#FF4A76">
                                        <span class="list-product__color-swatch"
                                            style="background-color:#FF4A76"></span>
                                    </div>
                                    <div class="list-product__color-option">
                                        <input class="list-product__color-input" type="radio" id="radioNoLabel09"
                                            name="radioNoLabel" value="#3E68FF">
                                        <span class="list-product__color-swatch"
                                            style="background-color:#3E68FF"></span>
                                    </div>
                                    <div class="list-product__color-option">
                                        <input class="list-product__color-input" type="radio" id="radioNoLabel10"
                                            name="radioNoLabel" value="#7BEF68">
                                        <span class="list-product__color-swatch"
                                            style="background-color:#7BEF68"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Size Widget -->
                            <div class="list-product__widget">
                                <h6 class="list-product__widget-title">Size</h6>
                                <div class="list-product__size-group">
                                    <input type="radio" class="list-product__size-input" id="btnradio101"
                                        name="btnradio1" checked="">
                                    <label class="list-product__size-label" for="btnradio101">4</label>
                                    <input type="radio" class="list-product__size-input" id="btnradiol02"
                                        name="btnradio1">
                                    <label class="list-product__size-label" for="btnradiol02">6</label>
                                    <input type="radio" class="list-product__size-input" id="btnradiol03"
                                        name="btnradio1">
                                    <label class="list-product__size-label" for="btnradiol03">8</label>
                                    <input type="radio" class="list-product__size-input" id="btnradiol04"
                                        name="btnradio1">
                                    <label class="list-product__size-label" for="btnradiol04">10</label>
                                    <input type="radio" class="list-product__size-input" id="btnradiol05"
                                        name="btnradio1">
                                    <label class="list-product__size-label" for="btnradiol05">12</label>
                                    <input type="radio" class="list-product__size-input" id="btnradiol06"
                                        name="btnradio1">
                                    <label class="list-product__size-label" for="btnradiol06">14</label>
                                    <input type="radio" class="list-product__size-input" id="btnradiol07"
                                        name="btnradio1">
                                    <label class="list-product__size-label" for="btnradiol07">16</label>
                                    <input type="radio" class="list-product__size-input" id="btnradiol08"
                                        name="btnradio1">
                                    <label class="list-product__size-label" for="btnradiol08">18</label>
                                    <input type="radio" class="list-product__size-input" id="btnradiol09"
                                        name="btnradio1">
                                    <label class="list-product__size-label" for="btnradiol09">20</label>
                                </div>
                            </div>

                            <!-- Category Widget -->
                            <div class="list-product__widget list-product__widget--categories">
                                <h6 class="list-product__widget-title">Category</h6>
                                <ul class="list-product__category-list">
                                    <li class="list-product__category-item">
                                        <a href="/blog-category" class="list-product__category-link">Dresses</a>
                                        <span class="list-product__category-count">(10)</span>
                                    </li>
                                    <li class="list-product__category-item">
                                        <a href="/blog-category" class="list-product__category-link">Top & Blouses</a>
                                        <span class="list-product__category-count">(5)</span>
                                    </li>
                                    <li class="list-product__category-item">
                                        <a href="/blog-category" class="list-product__category-link">Boots</a>
                                        <span class="list-product__category-count">(17)</span>
                                    </li>
                                    <li class="list-product__category-item">
                                        <a href="/blog-category" class="list-product__category-link">Jewelry</a>
                                        <span class="list-product__category-count">(13)</span>
                                    </li>
                                    <li class="list-product__category-item">
                                        <a href="/blog-category" class="list-product__category-link">Makeup</a>
                                        <span class="list-product__category-count">(11)</span>
                                    </li>
                                    <li class="list-product__category-item">
                                        <a href="/blog-category" class="list-product__category-link">Fragrances</a>
                                        <span class="list-product__category-count">(17)</span>
                                    </li>
                                    <li class="list-product__category-item">
                                        <a href="/blog-category" class="list-product__category-link">Shaving &
                                            Grooming</a>
                                        <span class="list-product__category-count">(13)</span>
                                    </li>
                                    <li class="list-product__category-item">
                                        <a href="/blog-category" class="list-product__category-link">Jacket</a>
                                        <span class="list-product__category-count">(12)</span>
                                    </li>
                                    <li class="list-product__category-item">
                                        <a href="/blog-category" class="list-product__category-link">Coat</a>
                                        <span class="list-product__category-count">(22)</span>
                                    </li>
                                </ul>
                            </div>

                            <!-- Tags Widget -->
                            <div class="list-product__widget list-product__widget--tags">
                                <h6 class="list-product__widget-title">Tags</h6>
                                <div class="list-product__tag-cloud">
                                    <a href="/blog-tag" class="list-product__tag">Vintage</a>
                                    <a href="/blog-tag" class="list-product__tag">Wedding</a>
                                    <a href="/blog-tag" class="list-product__tag">Cotton</a>
                                    <a href="/blog-tag" class="list-product__tag">Linen</a>
                                    <a href="/blog-tag" class="list-product__tag">Navy</a>
                                    <a href="/blog-tag" class="list-product__tag">Urban</a>
                                    <a href="/blog-tag" class="list-product__tag">Business Meeting</a>
                                    <a href="/blog-tag" class="list-product__tag">Formal</a>
                                </div>
                            </div>

                            <!-- Reset Button -->
                            <a class="list-product__reset-btn" href="#">RESET</a>
                        </aside>
                    </div>
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="list-product__main">
                <div class="list-product__filter-wrapper">
                    <div class="list-product__filter-left">
                        <ul class="list-product__filter-tags">
                            <li class="list-product__filter-tag">
                                <a class="list-product__tag-btn" href="#">
                                    Dresses<i class="icon feather icon-x list-product__tag-close"></i>
                                </a>
                            </li>
                            <li class="list-product__filter-tag">
                                <a class="list-product__tag-btn" href="#">
                                    Tops<i class="icon feather icon-x list-product__tag-close"></i>
                                </a>
                            </li>
                        </ul>
                        <span class="list-product__results-text">Showing 1–5 Of 50 Results</span>
                    </div>

                    <div class="list-product__filter-right">
                        <a class="list-product__panel-btn" href="#">
                            <svg class="list-product__panel-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25 25"
                                width="20" height="20">
                                <g id="Layer_28" data-name="Layer 28">
                                    <path
                                        d="M2.54,5H15v.5A1.5,1.5,0,0,0,16.5,7h2A1.5,1.5,0,0,0,20,5.5V5h2.33a.5.5,0,0,0,0-1H20V3.5A1.5,1.5,0,0,0,18.5,2h-2A1.5,1.5,0,0,0,15,3.5V4H2.54a.5.5,0,0,0,0,1ZM16,3.5a.5.5,0,0,1,.5-.5h2a.5.5,0,0,1,.5.5v2a.5.5,0,0,1-.5.5h-2a.5.5,0,0,1-.5-.5Z">
                                    </path>
                                    <path
                                        d="M22.4,20H18v-.5A1.5,1.5,0,0,0,16.5,18h-2A1.5,1.5,0,0,0,13,19.5V20H2.55a.5.5,0,0,0,0,1H13v.5A1.5,1.5,0,0,0,14.5,23h2A1.5,1.5,0,0,0,18,21.5V21h4.4a.5.5,0,0,0,0-1ZM17,21.5a.5.5,0,0,1-.5.5h-2a.5.5,0,0,1-.5-.5v-2a.5.5,0,0,1,.5-.5h2a.5.5,0,0,1,.5.5Z">
                                    </path>
                                    <path
                                        d="M8.5,15h2A1.5,1.5,0,0,0,12,13.5V13H22.45a.5.5,0,1,0,0-1H12v-.5A1.5,1.5,0,0,0,10.5,10h-2A1.5,1.5,0,0,0,7,11.5V12H2.6a.5.5,0,1,0,0,1H7v.5A1.5,1.5,0,0,0,8.5,15ZM8,11.5a.5.5,0,0,1,.5-.5h2a.5.5,0,0,1,.5.5v2a.5.5,0,0,1-.5.5h-2a.5.5,0,0,1-.5-.5Z">
                                    </path>
                                </g>
                            </svg>Filter
                        </a>

                        <div class="list-product__dropdown">
                            <button type="button" class="list-product__dropdown-btn">
                                <span class="list-product__dropdown-text">Latest</span>
                                <i class="fa-solid fa-angle-down list-product__dropdown-icon"></i>
                            </button>
                        </div>

                        <div class="list-product__dropdown list-product__dropdown--category">
                            <button type="button" class="list-product__dropdown-btn">
                                <span class="list-product__dropdown-text">Products</span>
                                <i class="fa-solid fa-angle-down list-product__dropdown-icon"></i>
                            </button>
                        </div>

                        <div class="list-product__view-tabs">
                            <ul class="list-product__tab-nav">
                                <li class="list-product__tab-item">
                                    <a class="list-product__tab-link" href="#">
                                        <svg width="512" height="512" viewBox="0 0 512 512" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <g clippath="url(#clip0_121_190)">
                                                <path
                                                    d="M42.667 373.333H96C119.564 373.333 138.667 392.436 138.667 416V469.333C138.667 492.898 119.564 512 96 512H42.667C19.103 512 0 492.898 0 469.333V416C0 392.436 19.103 373.333 42.667 373.333Z"
                                                    fill="black"></path>
                                                <path
                                                    d="M42.667 186.667H96C119.564 186.667 138.667 205.77 138.667 229.334V282.667C138.667 306.231 119.564 325.334 96 325.334H42.667C19.103 325.333 0 306.231 0 282.667V229.334C0 205.769 19.103 186.667 42.667 186.667Z"
                                                    fill="black"></path>
                                                <path
                                                    d="M42.667 0H96C119.564 0 138.667 19.103 138.667 42.667V96C138.667 119.564 119.564 138.667 96 138.667H42.667C19.103 138.667 0 119.564 0 96V42.667C0 19.103 19.103 0 42.667 0Z"
                                                    fill="black"></path>
                                                <path
                                                    d="M230.565 373.333H468.437C492.682 373.333 512.336 392.436 512.336 416V469.333C512.336 492.897 492.682 512 468.437 512H230.565C206.32 512 186.666 492.898 186.666 469.333V416C186.667 392.436 206.32 373.333 230.565 373.333Z"
                                                    fill="black"></path>
                                                <path
                                                    d="M230.565 186.667H468.437C492.682 186.667 512.336 205.77 512.336 229.334V282.667C512.336 306.231 492.682 325.334 468.437 325.334H230.565C206.32 325.334 186.666 306.231 186.666 282.667V229.334C186.667 205.769 206.32 186.667 230.565 186.667Z"
                                                    fill="black"></path>
                                                <path
                                                    d="M230.565 0H468.437C492.682 0 512.336 19.103 512.336 42.667V96C512.336 119.564 492.682 138.667 468.437 138.667H230.565C206.32 138.667 186.666 119.564 186.666 96V42.667C186.667 19.103 206.32 0 230.565 0Z"
                                                    fill="black"></path>
                                            </g>
                                        </svg>
                                    </a>
                                </li>
                                <li class="list-product__tab-item">
                                    <a class="list-product__tab-link" href="#">
                                        <svg xmlns="http://www.w3.org/2000/svg" version="1.1" id="Capa_1" x="0px"
                                            y="0px" viewBox="0 0 512 512" width="512" height="512">
                                            <g>
                                                <path
                                                    d="M85.333,0h64c47.128,0,85.333,38.205,85.333,85.333v64c0,47.128-38.205,85.333-85.333,85.333h-64   C38.205,234.667,0,196.462,0,149.333v-64C0,38.205,38.205,0,85.333,0z">
                                                </path>
                                                <path
                                                    d="M362.667,0h64C473.795,0,512,38.205,512,85.333v64c0,47.128-38.205,85.333-85.333,85.333h-64   c-47.128,0-85.333-38.205-85.333-85.333v-64C277.333,38.205,315.538,0,362.667,0z">
                                                </path>
                                                <path
                                                    d="M85.333,277.333h64c47.128,0,85.333,38.205,85.333,85.333v64c0,47.128-38.205,85.333-85.333,85.333h-64   C38.205,512,0,473.795,0,426.667v-64C0,315.538,38.205,277.333,85.333,277.333z">
                                                </path>
                                                <path
                                                    d="M362.667,277.333h64c47.128,0,85.333,38.205,85.333,85.333v64C512,473.795,473.795,512,426.667,512h-64   c-47.128,0-85.333-38.205-85.333-85.333v-64C277.333,315.538,315.538,277.333,362.667,277.333z">
                                                </path>
                                            </g>
                                        </svg>
                                    </a>
                                </li>
                                <li class="list-product__tab-item">
                                    <a class="list-product__tab-link list-product__tab-link--active" href="#">
                                        <svg xmlns="http://www.w3.org/2000/svg" version="1.1" id="Capa_2" x="0px"
                                            y="0px" viewBox="0 0 512 512" width="512" height="512">
                                            <g>
                                                <path
                                                    d="M42.667,373.333H96c23.564,0,42.667,19.103,42.667,42.667v53.333C138.667,492.898,119.564,512,96,512H42.667   C19.103,512,0,492.898,0,469.333V416C0,392.436,19.103,373.333,42.667,373.333z">
                                                </path>
                                                <path
                                                    d="M416,373.333h53.333C492.898,373.333,512,392.436,512,416v53.333C512,492.898,492.898,512,469.333,512H416   c-23.564,0-42.667-19.102-42.667-42.667V416C373.333,392.436,392.436,373.333,416,373.333z">
                                                </path>
                                                <path
                                                    d="M42.667,186.667H96c23.564,0,42.667,19.103,42.667,42.667v53.333c0,23.564-19.103,42.667-42.667,42.667H42.667   C19.103,325.333,0,306.231,0,282.667v-53.333C0,205.769,19.103,186.667,42.667,186.667z">
                                                </path>
                                                <path
                                                    d="M416,186.667h53.333c23.564,0,42.667,19.103,42.667,42.667v53.333c0,23.564-19.102,42.667-42.667,42.667H416   c-23.564,0-42.667-19.103-42.667-42.667v-53.333C373.333,205.769,392.436,186.667,416,186.667z">
                                                </path>
                                                <path
                                                    d="M42.667,0H96c23.564,0,42.667,19.103,42.667,42.667V96c0,23.564-19.103,42.667-42.667,42.667H42.667   C19.103,138.667,0,119.564,0,96V42.667C0,19.103,19.103,0,42.667,0z">
                                                </path>
                                                <path
                                                    d="M229.333,373.333h53.333c23.564,0,42.667,19.103,42.667,42.667v53.333c0,23.564-19.103,42.667-42.667,42.667h-53.333   c-23.564,0-42.667-19.102-42.667-42.667V416C186.667,392.436,205.769,373.333,229.333,373.333z">
                                                </path>
                                                <path
                                                    d="M229.333,186.667h53.333c23.564,0,42.667,19.103,42.667,42.667v53.333c0,23.564-19.103,42.667-42.667,42.667h-53.333   c-23.564,0-42.667-19.103-42.667-42.667v-53.333C186.667,205.769,205.769,186.667,229.333,186.667z">
                                                </path>
                                                <path
                                                    d="M229.333,0h53.333c23.564,0,42.667,19.103,42.667,42.667V96c0,23.564-19.103,42.667-42.667,42.667h-53.333   c-23.564,0-42.667-19.103-42.667-42.667V42.667C186.667,19.103,205.769,0,229.333,0z">
                                                </path>
                                                <path
                                                    d="M416,0h53.333C492.898,0,512,19.103,512,42.667V96c0,23.564-19.102,42.667-42.667,42.667H416   c-23.564,0-42.667-19.103-42.667-42.667V42.667C373.333,19.103,392.436,0,416,0z">
                                                </path>
                                            </g>
                                        </svg>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Product Grid Content -->
                <div class="list-product__content">
                    <div class="list-product__grid">
                        @partials.ProductItem("")
                        @partials.ProductItem("")
                        @partials.ProductItem("")
                        @partials.ProductItem("")
                        @partials.ProductItem("")
                        @partials.ProductItem("")
                        @partials.ProductItem("")
                        @partials.ProductItem("")
                        @partials.ProductItem("")
                        @partials.ProductItem("")
                        @partials.ProductItem("")
                        @partials.ProductItem("")
                    </div>
                </div>

                <!-- Pagination -->
                <div class="list-product__pagination">
                    <div class="list-product__pagination-info">
                        <p class="list-product__pagination-text">Showing 1–5 of 50 Results</p>
                    </div>
                    <div class="list-product__pagination-nav">
                        <nav aria-label="Product Pagination">
                            <ul class="list-product__pagination-list">
                                <li class="list-product__pagination-item">
                                    <a class="list-product__pagination-link list-product__pagination-link--active"
                                        href="#">1</a>
                                </li>
                                <li class="list-product__pagination-item">
                                    <a class="list-product__pagination-link" href="#">2</a>
                                </li>
                                <li class="list-product__pagination-item">
                                    <a class="list-product__pagination-link" href="#">3</a>
                                </li>
                                <li class="list-product__pagination-item">
                                    <a class="list-product__pagination-link list-product__pagination-link--next"
                                        href="#">Next</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
}