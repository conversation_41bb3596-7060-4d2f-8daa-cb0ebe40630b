package components

import (
    "github.com/networld-solution/gos/templates"
)

templ SlideshowHome(){
    <section class="banner-slides">
        <div class="swiper-wrapper">
            <div class="swiper-slide banner-item">
                <a href="#">                
                    <img class="slideshow__img" src={templates.AssetURL("/static/images/slideshow/slider_1.webp")} alt="slideshow 1">
                </a>
            </div>

            <div class="swiper-slide banner-item">
                <a href="#">
                    <img class="slideshow__img" src={templates.AssetURL("/static/images/slideshow/slider_2.jpg")} alt="slideshow 2">
                </a>
            </div>
        </div>
        <div class="swiper-pagination"></div>
    </section>
}