package header

import (
    "github.com/networld-solution/gos/templates"
)
templ HeaderTop() {
<div class="header-info-bar">
    <div class="container90">
        <div class="header-top-wrapper"> 
            <a class="header-logo" href="#" title="Maybi">                       
                <img class="header-logo__img" width="150" height="42" src={templates.AssetURL("/static/images/logo.webp")} alt="Maybi Ecommerce Logo"> 
            </a>  
            <div class="header-support">
                <div class="header-support__icon">
                    <svg><use xlink:href="#icon-support"></use></svg>
                </div>                        
                <div class="header-support__content">
                    <span>24/7 SUPPORT</span>
                    <h6>+84 911 571 166</h6>
                </div>
            </div>
            <div class="header-search">
                <form class="header-search__form">                            
                    <div class="search-category">
                        <div class="search-category__title" id="search-cate" data-value="all">
                            Tất cả
                        </div>
                        <ul class="search-category__dropdown">                                    
                            <li data-value="all" class="active">T<PERSON>t cả</li>
                            <li class="group">Đầm</li>
                            <li data-value="1">Đầm 2 dây</li>
                            <li data-value="2">Đầm thun</li>
                            <li data-value="3">Đầm ôm</li>
                            <li data-value="4">Đầm xoè</li> 
                            <li data-value="5">Đầm suông</li>
                            <li data-value="6">Đầm hoạ tiết</li>
                            <li class="group">Áo</li>
                            <li data-value="7">Croptop kiểu</li>
                            <li data-value="8">Croptop thun</li>
                            <li data-value="9">Áo sơ mi</li>
                            <li data-value="10">Áo 2 dây</li>                                  
                        </ul>
                    </div> 
                    <input type="text" class="header-search__input border" name="q" value="" placeholder="Bạn tìm gì...">
                    <button type="button" class="header-search__btn">
                        <i class="ri-search-2-line"></i>
                    </button>                                                        
                </form>
            </div>
        </div>
    </div>
</div>
}