package components

import (
    "goweb/views/partials"
)

templ NewArrivals() {
<section class="sec">
    <div class="container90">
        <div class="sec-head">
            <h2 class="sec-head__title"><PERSON><PERSON><PERSON> mới về</h2>
            <div class="sec-head__btn">
                <a href="#" class="btn-primary"><PERSON>em tất cả</a>
            </div>
        </div>
        <div class="product-list">
            {{z:=1}}
            for i:=1;i<=5; i++ {
                @partials.ProductItem("")
                {{z = z+3}}
            }
        </div>
    </div>
</section>
}