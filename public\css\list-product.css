/* Shop Sidebar Background Section */
.shop-sidebar-bg {
    background-image: url('/images/bg/shop-sidebar-bg.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 300px;
    position: relative;
    display: flex;
    align-items: center;
}

.dz-bnr-inr {
    padding: 80px 0;
    position: relative;
}

.overlay-black-light::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
}

.dz-bnr-inr-entry {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
}

.dz-bnr-inr-entry h1 {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 20px;
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.breadcrumb-row {
    margin-top: 15px;
}

.breadcrumb {
    display: flex;
    justify-content: center;
    align-items: center;
    list-style: none;
    padding: 0;
    margin: 0;
    background: transparent;
}

.breadcrumb-item {
    color: white;
    font-size: 16px;
    font-weight: 500;
}

.breadcrumb-item+.breadcrumb-item::before {
    content: '>';
    margin: 0 10px;
    color: white;
    opacity: 0.7;
}

.breadcrumb-item a {
    color: white;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-item a:hover {
    color: #f8f9fa;
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dz-bnr-inr {
        padding: 60px 0;
    }

    .dz-bnr-inr-entry h1 {
        font-size: 36px;
    }

    .breadcrumb-item {
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .dz-bnr-inr {
        padding: 40px 0;
    }

    .dz-bnr-inr-entry h1 {
        font-size: 28px;
    }

    .shop-sidebar-bg {
        min-height: 200px;
    }
}