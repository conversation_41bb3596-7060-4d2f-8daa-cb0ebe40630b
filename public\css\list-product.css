/* List Product Section - BEM Structure */
.list-product {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 18.75em;
    position: relative;
    display: flex;
    align-items: center;
    padding: 5em 0;
    font-size: var(--10px);
}

.list-product--with-overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
}

.list-product__container {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 75em;
    margin: 0 auto;
    padding: 0 0.9375em;
}

.list-product__content {
    text-align: center;
    color: white;
}

.list-product__title {
    font-size: 4em;
    font-weight: 700;
    margin-bottom: 0.5em;
    color: white;
    text-shadow: 0.125em 0.125em 0.25em rgba(0, 0, 0, 0.5);
}

.list-product__breadcrumb {
    margin-top: 0.9375em;
}

.breadcrumb {
    display: flex;
    justify-content: center;
    align-items: center;
    list-style: none;
    padding: 0;
    margin: 0;
    background: transparent;
}

.breadcrumb__item {
    color: white;
    font-size: 1.6em;
    font-weight: 500;
}

.breadcrumb__item+.breadcrumb__item::before {
    content: '>';
    margin: 0 0.625em;
    color: white;
    opacity: 0.7;
}

.breadcrumb__link {
    color: white;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb__link:hover {
    color: #f8f9fa;
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 48em) {
    .list-product {
        padding: 3.75em 0;
    }

    .list-product__title {
        font-size: 2.25em;
    }

    .breadcrumb__item {
        font-size: 0.875em;
    }
}

@media (max-width: 30em) {
    .list-product {
        padding: 2.5em 0;
        min-height: 12.5em;
    }

    .list-product__title {
        font-size: 1.75em;
    }
}