/* List Product Section */
.list-product {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 18.75em;
    position: relative;
    display: flex;
    align-items: center;
    padding: 5em 0;
    font-size: var(--10px);
}

.list-product--with-overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
}

.list-product__container {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 75em;
    margin: 0 auto;
    padding: 0 0.9375em;
}

.list-product__content {
    text-align: center;
    color: white;
}

.list-product__title {
    font-size: 4em;
    font-weight: 700;
    margin-bottom: 0.5em;
    color: white;
    text-shadow: 0.125em 0.125em 0.25em rgba(0, 0, 0, 0.5);
}

.list-product__breadcrumb {
    margin-top: 0.9375em;
}

.breadcrumb {
    display: flex;
    justify-content: center;
    align-items: center;
    list-style: none;
    padding: 0;
    margin: 0;
    background: transparent;
}

.breadcrumb__item {
    color: white;
    font-size: 1.6em;
    font-weight: 500;
}

.breadcrumb__item+.breadcrumb__item::before {
    content: '>';
    margin: 0 0.625em;
    color: white;
    opacity: 0.7;
}

.breadcrumb__link {
    color: white;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb__link:hover {
    color: #f8f9fa;
    text-decoration: underline;
}

/* List Product Main Section */
.list-product {
    padding: 1.875em 0;
}

.list-product__container {
    width: 100%;
    max-width: 75em;
    margin: 0 auto;
    padding: 0 0.9375em;
}

.list-product__layout {
    display: flex;
    gap: 1.875em;
}

/* Sidebar Styles */
.list-product__sidebar {
    flex: 3;
    font-size: var(--10px);
}

.list-product__sidebar-sticky {
    position: sticky;
    top: 1.25em;
}

.list-product__close-btn {
    display: none;
    position: absolute;
    top: -2.5em;
    right: 0;
    width: 2.1875em;
    height: 2.1875em;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    z-index: 10;
}

.list-product__filter {
    padding: 1.5em;
}

.list-product__filter-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.875em;
}

.list-product__filter-title {
    font-size: 1.6em;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
}

.list-product__filter-icon {
    margin-right: 0.75em;
    font-size: 1.25em;
}

/* Widget Styles */
.list-product__widget {
    margin-bottom: 1.875em;
    transition: all 0.3s ease;
}

.list-product__widget:last-child {
    margin-bottom: 0;
}

.list-product__widget--active {
    padding: 0em;
}

.list-product__widget-title {
    font-size: 1.6em;
    font-weight: 600;
    margin-bottom: 1em;
    color: rgba(var(--rgb-black), 0.8);
}

/* Search Widget */
.list-product__widget--search {
    margin-bottom: 1.875em;
}

.list-product__search-form {
    position: relative;
}

.list-product__input-group {
    position: relative;
    display: flex;
}

.list-product__search-input {
    width: 100%;
    padding: 0.75em 3em 0.75em 1em;
    border: 0.0625em solid var(--secondary);
    border-radius: 0.25em;
    outline: none;
    transition: all 0.3s linear;
}

.list-product__search-input:focus {
    border-color: var(--rgba-primary-8);
    box-shadow: var(--rgba-primary-8) 0px 3px 6px;
    /* box-shadow: var(--rgba-primary-8) 0px 1px 4px, var(--rgba-primary-8) 0px 0px 0px 3px; */
}

.list-product__input-addon {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
}

.list-product__search-btn {
    height: 100%;
    padding: 0 1em;
    background: transparent;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Price Slider */
.list-product__price-slider {
    padding: 1em 0;
}

.list-product__range-slider {
    margin-bottom: 1em;
}

.list-product__slider {
    margin-bottom: 1.875em;
    height: 0.4em;
}

.list-product__price-value {
    display: block;
    font-size: 0.875em;
    color: #666;
    margin-bottom: 0.5em;
}

/* noUiSlider Custom Styling - Match the image exactly */
.list-product__slider .noUi-target {
    background: #f8f9fa;
    border: 0.0625em solid #dee2e6;
    border-radius: 0.375em;
    box-shadow: none;
    height: 0.25em;
    margin: 1.5em 0;
}

.list-product__slider .noUi-base {
    height: 100%;
    border-radius: 0.375em;
}

.list-product__slider .noUi-connects {
    border-radius: 0.375em;
    overflow: hidden;
}

.list-product__slider .noUi-connect {
    background: var(--rgba-primary-8);
    border-radius: 0;
}

.list-product__slider .noUi-handle {
    background: white;
    border: 0.1875em solid var(--rgba-primary-8);
    border-radius: 50%;
    box-shadow: 0 0.125em 0.25em rgba(0, 0, 0, 0.1);
    cursor: pointer;
    width: 1.5em;
    height: 1.5em;
    top: -0.625em;
    right: -0.75em;
    outline: none;
}

.list-product__slider .noUi-handle:before,
.list-product__slider .noUi-handle:after {
    display: none;
}

.list-product__slider .noUi-handle:hover {
    border-color: var(--rgba-primary-8);
    box-shadow: 0 0.125em 0.375em rgba(0, 0, 0, 0.15);
}

.list-product__slider .noUi-handle:active {
    border-color: #343a40;
    transform: scale(1.05);
}

.list-product__slider .noUi-handle.noUi-active {
    transform: scale(1.05);
}

/* Ensure proper touch area */
.list-product__slider .noUi-touch-area {
    height: 100%;
    width: 100%;
}

/* Remove tooltips */
.list-product__slider .noUi-tooltip {
    display: none;
}

/* Fix any extra handles/touch areas */
.list-product__slider .noUi-handle:nth-child(n+3) {
    display: none !important;
}

/* Price values container */
.list-product__price-values {
    display: flex;
    justify-content: space-between;
    margin-top: 0.75em;
}

/* Price value styling to match the image */
.list-product__price-value {
    display: inline-block;
    font-size: 1.4em;
    color: var(--black);
    font-weight: 500;
    margin: 0;
}

/* Color Filter */
.list-product__color-filter {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.75em;
    padding-left: 0.125em;
}

.list-product__color-option {
    position: relative;
}

.list-product__color-input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.list-product__color-swatch {
    display: block;
    width: 1.875em;
    height: 1.875em;
    border-radius: 50%;
    border: 0.125em solid #fff;
    box-shadow: 0 0 0 0.0625em #ddd;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    position: relative;
}

.list-product__color-input:checked+.list-product__color-swatch {
    transform: scale(1.1);
    box-shadow: 0 0 0 0.125em #0D775E;
}

.list-product__color-input:checked+.list-product__color-swatch::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 0.5em;
    height: 0.5em;
    background: white;
    border-radius: 50%;
    box-shadow: 0 0.0625em 0.125em rgba(0, 0, 0, 0.2);
}

/* Active color option styling */
.list-product__color-option--active {
    outline: 2px solid #000;
    border-radius: 50%;
}

/* Size Group */
.list-product__size-group {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5em;
}

.list-product__size-input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.list-product__size-label {
    display: inline-block;
    padding: 0.5em 0.875em;
    border: 0.0625em solid #ddd;
    border-radius: 0.25em;
    font-size: 0.875em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #fff;
    color: #333;
}

.list-product__size-input:checked+.list-product__size-label {
    background: #0D775E;
    color: white;
    border-color: #0D775E;
}

.list-product__size-label:hover {
    border-color: #0D775E;
}

/* Category Widget */
.list-product__widget--categories {
    margin-bottom: 1.875em;
}

.list-product__category-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.list-product__category-item {
    margin-bottom: 0.75em;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.list-product__category-item:last-child {
    margin-bottom: 0;
}

.list-product__category-link {
    color: #333;
    text-decoration: none;
    font-size: 0.875em;
    transition: color 0.2s ease;
}

.list-product__category-link:hover {
    color: #0D775E;
}

.list-product__category-count {
    font-size: 0.75em;
    color: #666;
    background: #f5f5f5;
    padding: 0.25em 0.5em;
    border-radius: 0.75em;
    min-width: 1.5em;
    text-align: center;
}

/* Tags Widget */
.list-product__widget--tags {
    margin-bottom: 1.875em;
}

.list-product__tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5em;
}

.list-product__tag {
    display: inline-block;
    padding: 0.375em 0.75em;
    background: #f5f5f5;
    color: #333;
    text-decoration: none;
    font-size: 0.75em;
    border-radius: 1.25em;
    transition: all 0.2s ease;
}

.list-product__tag:hover {
    background: #0D775E;
    color: white;
}

/* Reset Button */
.list-product__reset-btn {
    display: inline-block;
    width: 100%;
    padding: 0.75em 1.5em;
    background: #0D775E;
    color: white;
    text-decoration: none;
    text-align: center;
    font-size: 0.875em;
    font-weight: 600;
    border-radius: 0.25em;
    transition: background-color 0.2s ease;
    margin-top: 1.875em;
}

.list-product__reset-btn:hover {
    background: #0a5d4a;
    color: white;
}

/* Main Content Area */
.list-product__main {
    flex: 1;
    min-width: 0;
}

.list-product__filter-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.875em;
    padding: 1.25em;
    background: #fff;
    border-radius: 0.5em;
    box-shadow: 0 0.125em 0.5em rgba(0, 0, 0, 0.1);
    flex-wrap: wrap;
    gap: 1em;
}

.list-product__filter-left {
    display: flex;
    align-items: center;
    gap: 1em;
    flex-wrap: wrap;
}

.list-product__filter-right {
    display: flex;
    align-items: center;
    gap: 0.75em;
    flex-wrap: wrap;
}

/* Filter Tags */
.list-product__filter-tags {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 0.5em;
    flex-wrap: wrap;
}

.list-product__filter-tag {
    margin: 0;
}

.list-product__tag-btn {
    display: flex;
    align-items: center;
    gap: 0.5em;
    padding: 0.375em 0.75em;
    background: #0D775E;
    color: white;
    text-decoration: none;
    font-size: 0.75em;
    border-radius: 1.25em;
    transition: background-color 0.2s ease;
}

.list-product__tag-btn:hover {
    background: #0a5d4a;
    color: white;
}

.list-product__tag-close {
    font-size: 0.875em;
    margin-left: 0.25em;
}

.list-product__results-text {
    font-size: 0.875em;
    color: #666;
    white-space: nowrap;
}

/* Panel Button */
.list-product__panel-btn {
    display: flex;
    align-items: center;
    gap: 0.5em;
    padding: 0.5em 1em;
    background: #f5f5f5;
    color: #333;
    text-decoration: none;
    font-size: 0.875em;
    border-radius: 0.25em;
    transition: background-color 0.2s ease;
}

.list-product__panel-btn:hover {
    background: #e9ecef;
    color: #333;
}

.list-product__panel-icon {
    width: 1.25em;
    height: 1.25em;
}

/* Dropdown Styles */
.list-product__dropdown {
    position: relative;
}

.list-product__dropdown-btn {
    display: flex;
    align-items: center;
    gap: 0.5em;
    padding: 0.5em 1em;
    background: #fff;
    border: 0.0625em solid #ddd;
    border-radius: 0.25em;
    font-size: 0.875em;
    cursor: pointer;
    transition: border-color 0.2s ease;
}

.list-product__dropdown-btn:hover {
    border-color: #0D775E;
}

.list-product__dropdown-text {
    color: #333;
}

.list-product__dropdown-icon {
    font-size: 0.75em;
    color: #666;
}

/* View Tabs */
.list-product__view-tabs {
    display: flex;
}

.list-product__tab-nav {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    border: 0.0625em solid #ddd;
    border-radius: 0.25em;
    overflow: hidden;
}

.list-product__tab-item {
    margin: 0;
}

.list-product__tab-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5em;
    height: 2.5em;
    background: #fff;
    border-right: 0.0625em solid #ddd;
    transition: background-color 0.2s ease;
    text-decoration: none;
}

.list-product__tab-item:last-child .list-product__tab-link {
    border-right: none;
}

.list-product__tab-link:hover {
    background: #f5f5f5;
}

.list-product__tab-link--active {
    background: #0D775E;
}

.list-product__tab-link svg {
    width: 1em;
    height: 1em;
    fill: #666;
    transition: fill 0.2s ease;
}

.list-product__tab-link:hover svg {
    fill: #333;
}

.list-product__tab-link--active svg {
    fill: white;
}

/* Product Grid */
.list-product__content {
    margin-bottom: 1.875em;
}

.list-product__grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(25em, 1fr));
    gap: 1.875em;
    transition: all 0.3s ease;
}

/* Grid View Variations */
.list-product__grid--list {
    grid-template-columns: 1fr;
    gap: 1.25em;
}

.list-product__grid--grid-2 {
    grid-template-columns: repeat(auto-fill, minmax(20em, 1fr));
    gap: 1.5em;
}

.list-product__grid--grid-3 {
    grid-template-columns: repeat(auto-fill, minmax(18.75em, 1fr));
    gap: 1.875em;
}

/* Mobile Sidebar */
.list-product__sidebar--open {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: white;
    z-index: 1000;
    overflow-y: auto;
    transform: translateX(0);
    transition: transform 0.3s ease;
}

.list-product__close-btn {
    display: none;
}

/* Body class when sidebar is open */
body.sidebar-open {
    overflow: hidden;
}

/* Pagination */
.list-product__pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.25em;
    background: #fff;
    border-radius: 0.5em;
    box-shadow: 0 0.125em 0.5em rgba(0, 0, 0, 0.1);
    flex-wrap: wrap;
    gap: 1em;
}

.list-product__pagination-info {
    flex: 1;
}

.list-product__pagination-text {
    font-size: 0.875em;
    color: #666;
    margin: 0;
}

.list-product__pagination-nav {
    flex: 0 0 auto;
}

.list-product__pagination-list {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 0.5em;
}

.list-product__pagination-item {
    margin: 0;
}

.list-product__pagination-link {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 2.5em;
    height: 2.5em;
    padding: 0 0.75em;
    background: #fff;
    border: 0.0625em solid #ddd;
    border-radius: 0.25em;
    color: #333;
    text-decoration: none;
    font-size: 0.875em;
    transition: all 0.2s ease;
}

.list-product__pagination-link:hover {
    background: #f5f5f5;
    border-color: #0D775E;
    color: #0D775E;
}

.list-product__pagination-link--active {
    background: #0D775E;
    border-color: #0D775E;
    color: white;
}

.list-product__pagination-link--active:hover {
    background: #0a5d4a;
    color: white;
}

/* Category Widget */
.list-product__widget--categories {
    margin-bottom: 1.875em;
}

.list-product__category-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.list-product__category-item {
    margin-bottom: 0.75em;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.list-product__category-item:last-child {
    margin-bottom: 0;
}

.list-product__category-link {
    color: #333;
    text-decoration: none;
    font-size: 0.875em;
    transition: color 0.2s ease;
}

.list-product__category-link:hover {
    color: #0D775E;
}

.list-product__category-count {
    font-size: 0.75em;
    color: #666;
    background: #f5f5f5;
    padding: 0.25em 0.5em;
    border-radius: 0.75em;
    min-width: 1.5em;
    text-align: center;
}

/* Tags Widget */
.list-product__widget--tags {
    margin-bottom: 1.875em;
}

.list-product__tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5em;
}

.list-product__tag {
    display: inline-block;
    padding: 0.375em 0.75em;
    background: #f5f5f5;
    color: #333;
    text-decoration: none;
    font-size: 0.75em;
    border-radius: 1.25em;
    transition: all 0.2s ease;
}

.list-product__tag:hover {
    background: #0D775E;
    color: white;
}

/* Reset Button */
.list-product__reset-btn {
    display: inline-block;
    width: 100%;
    padding: 0.75em 1.5em;
    background: #0D775E;
    color: white;
    text-decoration: none;
    text-align: center;
    font-size: 0.875em;
    font-weight: 600;
    border-radius: 0.25em;
    transition: background-color 0.2s ease;
    margin-top: 1.875em;
}

.list-product__reset-btn:hover {
    background: #0a5d4a;
    color: white;
}

/* Main Content Area */
.list-product__main {
    flex: 9;
}

.list-product__filter-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.875em;
    padding: 1.25em;
    background: #fff;
    border-radius: 0.5em;
    box-shadow: 0 0.125em 0.5em rgba(0, 0, 0, 0.1);
    flex-wrap: wrap;
    gap: 1em;
}

.list-product__filter-left {
    display: flex;
    align-items: center;
    gap: 1em;
    flex-wrap: wrap;
}

.list-product__filter-right {
    display: flex;
    align-items: center;
    gap: 0.75em;
    flex-wrap: wrap;
}

/* Filter Tags */
.list-product__filter-tags {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 0.5em;
    flex-wrap: wrap;
}

.list-product__filter-tag {
    margin: 0;
}

.list-product__tag-btn {
    display: flex;
    align-items: center;
    gap: 0.5em;
    padding: 0.375em 0.75em;
    background: #0D775E;
    color: white;
    text-decoration: none;
    font-size: 0.75em;
    border-radius: 1.25em;
    transition: background-color 0.2s ease;
}

.list-product__tag-btn:hover {
    background: #0a5d4a;
    color: white;
}

.list-product__tag-close {
    font-size: 0.875em;
    margin-left: 0.25em;
}

.list-product__results-text {
    font-size: 0.875em;
    color: #666;
    white-space: nowrap;
}

/* Panel Button */
.list-product__panel-btn {
    display: flex;
    align-items: center;
    gap: 0.5em;
    padding: 0.5em 1em;
    background: #f5f5f5;
    color: #333;
    text-decoration: none;
    font-size: 0.875em;
    border-radius: 0.25em;
    transition: background-color 0.2s ease;
}

.list-product__panel-btn:hover {
    background: #e9ecef;
    color: #333;
}

.list-product__panel-icon {
    width: 1.25em;
    height: 1.25em;
}

/* Responsive Design */
@media (max-width: 75em) {
    .list-product__grid {
        grid-template-columns: repeat(auto-fill, minmax(16.25em, 1fr));
        gap: 1.25em;
    }
}

@media (max-width: 64em) {
    .list-product__layout {
        gap: 1.25em;
    }

    .list-product__sidebar {
        flex: 0 0 16.25em;
        width: 16.25em;
    }

    .list-product__grid {
        grid-template-columns: repeat(auto-fill, minmax(15em, 1fr));
    }
}

@media (max-width: 48em) {
    .list-product {
        padding: 3.75em 0 1.875em;
    }

    .list-product__title {
        font-size: 2.25em;
    }

    .breadcrumb__item {
        font-size: 0.875em;
    }

    .list-product__layout {
        flex-direction: column;
        gap: 1.875em;
    }

    .list-product__sidebar {
        flex: none;
        width: 100%;
        order: 2;
        display: none;
        /* Hide sidebar by default on mobile */
    }

    .list-product__sidebar--open {
        display: block;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100vh;
        background: white;
        z-index: 1000;
        overflow-y: auto;
        padding: 1.25em;
        box-sizing: border-box;
    }

    .list-product__close-btn {
        display: flex;
        position: absolute;
        top: 1.25em;
        right: 1.25em;
        width: 2.5em;
        height: 2.5em;
        background: var(--rgba-primary-8);
        border-radius: 50%;
        align-items: center;
        justify-content: center;
        color: white;
        text-decoration: none;
        z-index: 10;
        font-size: 1.25em;
    }

    .list-product__main {
        order: 1;
    }

    .list-product__filter-wrapper {
        flex-direction: column;
        align-items: stretch;
        gap: 1em;
    }

    .list-product__filter-left,
    .list-product__filter-right {
        justify-content: center;
    }

    .list-product__grid {
        grid-template-columns: repeat(auto-fill, minmax(13.75em, 1fr));
        gap: 1.25em;
    }

    .list-product__grid--list {
        grid-template-columns: 1fr;
    }

    .list-product__grid--grid-2 {
        grid-template-columns: repeat(auto-fill, minmax(15em, 1fr));
    }

    .list-product__grid--grid-3 {
        grid-template-columns: repeat(auto-fill, minmax(12em, 1fr));
    }

    .list-product__pagination {
        flex-direction: column;
        text-align: center;
        gap: 1em;
    }

    .list-product__pagination-info {
        flex: none;
    }
}

@media (max-width: 36em) {
    .list-product__filter-right {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75em;
    }

    .list-product__view-tabs {
        justify-content: center;
    }

    .list-product__grid {
        grid-template-columns: repeat(auto-fill, minmax(12.5em, 1fr));
        gap: 1em;
    }

    .list-product__filter-tags {
        justify-content: center;
    }
}

@media (max-width: 30em) {
    .list-product {
        padding: 2.5em 0 1.25em;
        min-height: 12.5em;
    }

    .list-product__title {
        font-size: 1.75em;
    }

    .list-product__container {
        padding: 0 0.625em;
    }

    .list-product__filter {
        padding: 1em;
    }

    .list-product__filter-wrapper {
        padding: 1em;
    }

    .list-product__pagination {
        padding: 1em;
    }

    .list-product__grid {
        grid-template-columns: repeat(auto-fill, minmax(10em, 1fr));
        gap: 0.875em;
    }

    .list-product__widget-title {
        font-size: 1em;
    }

    .list-product__category-link,
    .list-product__search-input {
        font-size: 0.8125em;
    }
}