/* Shop Hero Section - BEM Structure */
.shop-hero {
    background-image: url('/images/bg/shop-sidebar-bg.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 300px;
    position: relative;
    display: flex;
    align-items: center;
    padding: 80px 0;
}

.shop-hero--with-overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
}

.shop-hero__container {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.shop-hero__content {
    text-align: center;
    color: white;
}

.shop-hero__title {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 20px;
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.shop-hero__breadcrumb {
    margin-top: 15px;
}

.breadcrumb {
    display: flex;
    justify-content: center;
    align-items: center;
    list-style: none;
    padding: 0;
    margin: 0;
    background: transparent;
}

.breadcrumb__item {
    color: white;
    font-size: 16px;
    font-weight: 500;
}

.breadcrumb__item+.breadcrumb__item::before {
    content: '>';
    margin: 0 10px;
    color: white;
    opacity: 0.7;
}

.breadcrumb__link {
    color: white;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb__link:hover {
    color: #f8f9fa;
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
    .shop-hero {
        padding: 60px 0;
    }

    .shop-hero__title {
        font-size: 36px;
    }

    .breadcrumb__item {
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .shop-hero {
        padding: 40px 0;
        min-height: 200px;
    }

    .shop-hero__title {
        font-size: 28px;
    }
}