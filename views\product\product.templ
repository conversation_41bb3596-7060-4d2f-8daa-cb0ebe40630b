package product

import (
    "goweb/views/layouts"
    "github.com/networld-solution/gos/templates"
	"goweb/views/product/components"
)

templ Product() {
    @layouts.Master(nil, &[]templ.Component{headProduct()}, scriptProduct()) { 
        @components.ProductBackground()
        @components.ListProduct()
    }
}

templ headProduct(){
	<link rel="stylesheet" href={templates.AssetURL("/static/css/list-product.css")}>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.7.1/nouislider.min.css">

}

templ scriptProduct() {
    <script src={templates.AssetURL("/static/js/list-product.js")}></script>
    <!-- noUiSlider JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.7.1/nouislider.min.js"></script>
}