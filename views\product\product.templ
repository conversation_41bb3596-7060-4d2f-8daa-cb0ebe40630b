package product

import (
    "goweb/views/layouts"
    "github.com/networld-solution/gos/templates"
	"goweb/views/product/components"
)

templ Product() {
    @layouts.Master(nil, &[]templ.Component{headProduct()}, scriptProduct()) { 
        @components.ProductBackground()
        @components.ListProduct()
    }
}

templ headProduct(){
	<link rel="stylesheet" href={templates.AssetURL("/static/css/list-product.css")}>
}

templ scriptProduct() {
    
}