package product

import (
	"github.com/networld-solution/gos/templates"
	"goweb/views/layouts"
	"goweb/views/product/components"
)

templ Product() {
	@layouts.Master(nil, &[]templ.Component{headProduct()}, scriptProduct()) {
		@components.ProductBackground()
		@components.ListProduct()
	}
}

templ headProduct() {
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.7.1/nouislider.min.css"/>
    <link rel="stylesheet" href={ templates.AssetURL("/static/css/list-product.css") }/>
}

templ scriptProduct() {
	<!-- noUiSlider JS -->
	<script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.7.1/nouislider.min.js"></script>
	<script src={ templates.AssetURL("/static/js/list-product.js") }></script>
}
