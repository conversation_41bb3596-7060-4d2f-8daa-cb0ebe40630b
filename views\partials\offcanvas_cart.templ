package partials

import (
    // "github.com/networld-solution/gos/templates"
)

templ OffcanvasCart() {
<div id="cartSidebar" class="sheet__overlay">
    <div class="sheet__panel panel__cart">
        <div class="sheet__header">
            <span class="sheet__title">Giỏ hàng</span>
            <button class="sheet__close">
                <svg>
                    <use href="#icon-close"></use>
                </svg>
            </button>
        </div>
        <div class="sheet__body">
            @FreeshipProgress()
            <div class="cart">
                <div class="cart__list">
                    // <div class="cart__item product">
                    //     <div class="cart__item__img">
                    //         <img class="product__img" src={templates.AssetURL("/static/images/blazer-green.jpg")}
                    //             alt="Áo blazer form rộng" />
                    //     </div>
                    //     <div class="cart__item__content product__info">
                    //         <h3 class="cart__item__title">
                    //             <a href="#" title="Áo blazer form rộng">Áo blazer form rộng</a>
                    //         </h3>
                    //         <div class="cart__item__info">
                    //             <div class="quantity-cart">
                    //                 <button class="quantity-cart__btn quantity-cart__btn-down" type="button">
                    //                     <svg class="product-icon__down">
                    //                         <use href="#icon-down"></use>
                    //                     </svg>
                    //                 </button>
                    //                 <input class="quantity-cart__input" type="text" value="1" />
                    //                 <button class="quantity-cart__btn quantity-cart__btn-up" type="button">
                    //                     <svg class="product-icon__up">
                    //                         <use href="#icon-up"></use>
                    //                     </svg>
                    //                 </button>
                    //             </div>
                    //             <div class="cart__item__price">
                    //                 <span class="cart__item__price--sale">10.000đ</span>
                    //             </div>
                    //         </div>
                    //         <div class="cart__item__variants">
                    //             <span class="text-muted">Size:</span>
                    //             <ul class="product-size">
                    //                 <li data-color='[{"color": "black", "img": "/static/images/blazer-den.jpg"}, {"color": "#607c2c", "img": "/static/images/blazer-xanh-reu.jpg"}]'
                    //                     class="product-size__item maybi-hover">S
                    //                 </li>
                    //                 <li data-color='[{"color": "black", "img": "/static/images/blazer-den.jpg"}, {"color": "#607c2c", "img": "/static/images/blazer-xanh-reu.jpg"}, {"color": "#225c48", "img": "/static/images/blazer-green.jpg"}, {"color": "#c9e6ff", "img": "/static/images/blazer-blue.jpg"}, {"color": "#f32250", "img": "/static/images/blazer-pink.jpg"}]'
                    //                     class="product-size__item maybi-hover">M
                    //                 </li>
                    //                 <li data-color='[{"color": "black", "img": "/static/images/blazer-den.jpg"}, {"color": "#607c2c", "img": "/static/images/blazer-xanh-reu.jpg"}, {"color": "#225c48", "img": "/static/images/blazer-green.jpg"}, {"color": "#c9e6ff", "img": "/static/images/blazer-blue.jpg"}, {"color": "#f32250", "img": "/static/images/blazer-pink.jpg"}, {"color": "#eb6609", "img": "/static/images/blazer-cam.jpg"}, {"color": "#ecdac1", "img": "/static/images/blazer-kem.jpg"}, {"color": "#ecdac1", "img": "/static/images/blazer-kem.jpg"}]'
                    //                     class="product-size__item maybi-hover product-size--active">L
                    //                 </li>
                    //             </ul>

                    //         </div>
                    //         <div class="cart__item__variants">
                    //             <span class="text-muted">Màu:</span>
                    //             <ul class="product-colors">
                    //                 <li class="product-color active"
                    //                     data-img={templates.AssetURL("/static/images/blazer-den.jpg")}>
                    //                     <span class="product-color__item" style="background-color: black;"></span>
                    //                 </li>
                    //                 <li class="product-color"
                    //                     data-img={templates.AssetURL("/static/images/blazer-xanh-reu.jpg")}>
                    //                     <span class="product-color__item" style="background-color: #607c2c;"></span>
                    //                 </li>
                    //                 <li class="product-color"
                    //                     data-img={templates.AssetURL("/static/images/blazer-green.jpg")}>
                    //                     <span class="product-color__item" style="background-color: #225c48;"></span>
                    //                 </li>
                    //                 <li class="product-color"
                    //                     data-img={templates.AssetURL("/static/images/blazer-blue.jpg")}>
                    //                     <span class="product-color__item" style="background-color: #c9e6ff;"></span>
                    //                 </li>
                    //                 <li class="product-color"
                    //                     data-img={templates.AssetURL("/static/images/blazer-pink.jpg")}>
                    //                     <span class="product-color__item" style="background-color: #f32250;"></span>
                    //                 </li>
                    //                 <li class="product-color"
                    //                     data-img={templates.AssetURL("/static/images/blazer-cam.jpg")}>
                    //                     <span class="product-color__item" style="background-color: #eb6609;"></span>
                    //                 </li>
                    //                 <li class="product-color"
                    //                     data-img={templates.AssetURL("/static/images/blazer-cam.jpg")}>
                    //                     <span class="product-color__item" style="background-color: #6e7ec3;"></span>
                    //                 </li>
                    //                 <li class="product-color"
                    //                     data-img={templates.AssetURL("/static/images/blazer-cam.jpg")}>
                    //                     <span class="product-color__item" style="background-color: #f7b789;"></span>
                    //                 </li>
                    //             </ul>
                    //         </div>

                    //     </div>
                    //     <div class="cart__item__delete">
                    //         <svg class="cart-icon__delete">
                    //             <use href="#icon-close"></use>
                    //         </svg>
                    //     </div>
                    // </div>
                </div>
            </div>
        </div>
        <div class="sheet__footer">
            <div class="cart__total">
                <div class="cart__total__item">
                    <h4 class="cart__total__label">
                        Tạm tính (<span class="cart__total__count">0</span> sản phẩm):
                    </h4>
                </div>
                <div class="cart__total__item">
                    <h4 class="cart__total__price"></h4>
                </div>
            </div>
            <div class="cart__action">
                <div class="cart__action__item">
                    <a href="#" class="w-100 btn-cart btn-secondary">Thanh toán</a>
                </div>
                <div class="cart__action__item">
                    <a href="#" class="w-100 btn-cart btn-primary">Xem giỏ hàng</a>
                </div>
            </div>
        </div>
    </div>
</div>
}