/**
 * List Product Page JavaScript
 * Handles price slider, filters, and interactive elements
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize Price Slider
    initializePriceSlider();
    
    // Initialize Filter Interactions
    initializeFilters();
    
    // Initialize Mobile Sidebar
    initializeMobileSidebar();
});

/**
 * Initialize Price Range Slider using noUiSlider
 */
function initializePriceSlider() {
    const slider = document.getElementById('slider-tooltips2');
    const minValueElement = document.getElementById('slider-margin-value-min2');
    const maxValueElement = document.getElementById('slider-margin-value-max2');
    
    if (!slider) return;
    
    // Check if noUiSlider is already initialized
    if (slider.noUiSlider) {
        slider.noUiSlider.destroy();
    }
    
    // Initialize noUiSlider
    noUiSlider.create(slider, {
        start: [40, 350],
        connect: true,
        range: {
            'min': 0,
            'max': 500
        },
        format: {
            to: function(value) {
                return Math.round(value);
            },
            from: function(value) {
                return Number(value);
            }
        },
        tooltips: [true, true]
    });
    
    // Update price values when slider changes
    slider.noUiSlider.on('update', function(values, handle) {
        const minPrice = values[0];
        const maxPrice = values[1];
        
        if (minValueElement) {
            minValueElement.textContent = `Min Price: $${minPrice}`;
        }
        
        if (maxValueElement) {
            maxValueElement.textContent = `Max Price: $${maxPrice}`;
        }
        
        // Trigger price filter event
        triggerPriceFilter(minPrice, maxPrice);
    });
    
    // Handle slider change (when user stops dragging)
    slider.noUiSlider.on('change', function(values, handle) {
        const minPrice = values[0];
        const maxPrice = values[1];
        
        console.log('Price range changed:', minPrice, '-', maxPrice);
        
        // Here you can add AJAX call to filter products
        filterProductsByPrice(minPrice, maxPrice);
    });
}

/**
 * Trigger price filter with debouncing
 */
let priceFilterTimeout;
function triggerPriceFilter(minPrice, maxPrice) {
    clearTimeout(priceFilterTimeout);
    priceFilterTimeout = setTimeout(() => {
        // Add visual feedback
        updatePriceFilterUI(minPrice, maxPrice);
    }, 300);
}

/**
 * Update price filter UI
 */
function updatePriceFilterUI(minPrice, maxPrice) {
    // Add active class to price widget
    const priceWidget = document.querySelector('.list-product__widget:has(#slider-tooltips2)');
    if (priceWidget) {
        priceWidget.classList.add('list-product__widget--active');
    }
    
    // Update results text
    const resultsText = document.querySelector('.list-product__results-text');
    if (resultsText) {
        resultsText.textContent = `Filtering by price: $${minPrice} - $${maxPrice}`;
    }
}

/**
 * Filter products by price (placeholder for AJAX implementation)
 */
function filterProductsByPrice(minPrice, maxPrice) {
    // This is where you would implement the actual filtering logic
    // For example, make an AJAX call to your server
    
    console.log('Filtering products by price range:', minPrice, '-', maxPrice);
    
    // Example AJAX call (uncomment and modify as needed):
    /*
    fetch('/api/products/filter', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            minPrice: minPrice,
            maxPrice: maxPrice
        })
    })
    .then(response => response.json())
    .then(data => {
        updateProductGrid(data.products);
    })
    .catch(error => {
        console.error('Error filtering products:', error);
    });
    */
}

/**
 * Initialize filter interactions
 */
function initializeFilters() {
    // Color filter
    const colorInputs = document.querySelectorAll('.list-product__color-input');
    colorInputs.forEach(input => {
        input.addEventListener('change', function() {
            const color = this.value;
            console.log('Color filter changed:', color);
            // Add your color filtering logic here
        });
    });
    
    // Size filter
    const sizeInputs = document.querySelectorAll('.list-product__size-input');
    sizeInputs.forEach(input => {
        input.addEventListener('change', function() {
            const size = this.value;
            console.log('Size filter changed:', size);
            // Add your size filtering logic here
        });
    });
    
    // Category filter
    const categoryLinks = document.querySelectorAll('.list-product__category-link');
    categoryLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const category = this.textContent.trim();
            console.log('Category filter clicked:', category);
            // Add your category filtering logic here
        });
    });
    
    // Tag filter
    const tagLinks = document.querySelectorAll('.list-product__tag');
    tagLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const tag = this.textContent.trim();
            console.log('Tag filter clicked:', tag);
            // Add your tag filtering logic here
        });
    });
    
    // Reset button
    const resetBtn = document.querySelector('.list-product__reset-btn');
    if (resetBtn) {
        resetBtn.addEventListener('click', function(e) {
            e.preventDefault();
            resetAllFilters();
        });
    }
    
    // Search form
    const searchForm = document.querySelector('.list-product__search-form');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const searchInput = this.querySelector('.list-product__search-input');
            const searchTerm = searchInput.value.trim();
            console.log('Search submitted:', searchTerm);
            // Add your search logic here
        });
    }
}

/**
 * Reset all filters
 */
function resetAllFilters() {
    // Reset price slider
    const slider = document.getElementById('slider-tooltips2');
    if (slider && slider.noUiSlider) {
        slider.noUiSlider.set([40, 350]);
    }
    
    // Reset color filters
    const colorInputs = document.querySelectorAll('.list-product__color-input');
    colorInputs.forEach(input => input.checked = false);
    
    // Reset size filters
    const sizeInputs = document.querySelectorAll('.list-product__size-input');
    sizeInputs.forEach(input => input.checked = false);
    
    // Reset search
    const searchInput = document.querySelector('.list-product__search-input');
    if (searchInput) {
        searchInput.value = '';
    }
    
    // Remove active classes
    const activeWidgets = document.querySelectorAll('.list-product__widget--active');
    activeWidgets.forEach(widget => widget.classList.remove('list-product__widget--active'));
    
    console.log('All filters reset');
}

/**
 * Initialize mobile sidebar functionality
 */
function initializeMobileSidebar() {
    const panelBtn = document.querySelector('.list-product__panel-btn');
    const sidebar = document.querySelector('.list-product__sidebar');
    const closeBtn = document.querySelector('.list-product__close-btn');
    
    if (panelBtn && sidebar) {
        panelBtn.addEventListener('click', function(e) {
            e.preventDefault();
            sidebar.classList.add('list-product__sidebar--open');
            document.body.classList.add('sidebar-open');
        });
    }
    
    if (closeBtn && sidebar) {
        closeBtn.addEventListener('click', function(e) {
            e.preventDefault();
            sidebar.classList.remove('list-product__sidebar--open');
            document.body.classList.remove('sidebar-open');
        });
    }
    
    // Close sidebar when clicking outside
    document.addEventListener('click', function(e) {
        if (sidebar && sidebar.classList.contains('list-product__sidebar--open')) {
            if (!sidebar.contains(e.target) && !panelBtn.contains(e.target)) {
                sidebar.classList.remove('list-product__sidebar--open');
                document.body.classList.remove('sidebar-open');
            }
        }
    });
}
