/**
 * List Product Page JavaScript
 * Handles price slider, filters, and interactive elements
 */

document.addEventListener('DOMContentLoaded', function () {

    // Initialize Price Slider
    initializePriceSlider();

    // Initialize Filter Interactions
    initializeFilters();

    // Initialize Mobile Sidebar
    initializeMobileSidebar();
});

/**
 * Initialize Price Range Slider using noUiSlider
 */
function initializePriceSlider() {
    const slider = document.getElementById('price-slider');
    const minValueElement = document.getElementById('price-min');
    const maxValueElement = document.getElementById('price-max');

    if (!slider) return;

    // Check if noUiSlider is already initialized
    if (slider.noUiSlider) {
        slider.noUiSlider.destroy();
    }

    // Initialize noUiSlider following standard format
    noUiSlider.create(slider, {
        start: [50, 366],
        connect: true,
        range: {
            'min': 0,
            'max': 500
        },
        step: 1,
        format: {
            to: function (value) {
                return Math.round(value);
            },
            from: function (value) {
                return Number(value);
            }
        }
    });

    // Update price values when slider changes
    slider.noUiSlider.on('update', function (values) {
        const minPrice = values[0];
        const maxPrice = values[1];

        if (minValueElement) {
            minValueElement.textContent = `Min Price: $${minPrice}`;
        }

        if (maxValueElement) {
            maxValueElement.textContent = `Max Price: $${maxPrice}`;
        }

        // Trigger price filter event
        triggerPriceFilter(minPrice, maxPrice);
    });

    // Handle slider change (when user stops dragging)
    slider.noUiSlider.on('change', function (values) {
        const minPrice = values[0];
        const maxPrice = values[1];

        console.log('Price range changed:', minPrice, '-', maxPrice);

        // Here you can add AJAX call to filter products
        filterProductsByPrice(minPrice, maxPrice);
    });
}

/**
 * Trigger price filter with debouncing
 */
let priceFilterTimeout;
function triggerPriceFilter(minPrice, maxPrice) {
    clearTimeout(priceFilterTimeout);
    priceFilterTimeout = setTimeout(() => {
        // Add visual feedback
        updatePriceFilterUI(minPrice, maxPrice);
    }, 300);
}

/**
 * Update price filter UI
 */
function updatePriceFilterUI(minPrice, maxPrice) {
    // Add active class to price widget
    const priceSlider = document.getElementById('price-slider');
    if (priceSlider) {
        const priceWidget = priceSlider.closest('.list-product__widget');
        if (priceWidget) {
            priceWidget.classList.add('list-product__widget--active');
        }
    }

    // Update results text
    const resultsText = document.querySelector('.list-product__results-text');
    if (resultsText) {
        resultsText.textContent = `Filtering by price: $${minPrice} - $${maxPrice}`;
    }
}

/**
 * Filter products by price - UI only
 */
function filterProductsByPrice(minPrice, maxPrice) {
    console.log('Price filter applied:', minPrice, '-', maxPrice);

    // Update UI to show filter is active
    updatePriceFilterUI(minPrice, maxPrice);
}

/**
 * Update color filter UI
 */
function updateColorFilterUI(selectedInput) {
    try {
        // Remove active class from all color options
        const allColorOptions = document.querySelectorAll('.list-product__color-option');
        allColorOptions.forEach(option => {
            option.classList.remove('list-product__color-option--active');
        });

        // Add active class to selected option
        const selectedOption = selectedInput.closest('.list-product__color-option');
        if (selectedOption) {
            selectedOption.classList.add('list-product__color-option--active');
        }

        console.log('Color filter UI updated for:', selectedInput.value);
    } catch (error) {
        console.error('Error updating color filter UI:', error);
    }
}

/**
 * Filter products by color - UI only
 */
function filterProductsByColor(color) {
    console.log('Color filter applied:', color);

    // Update results text to show active filter
    const resultsText = document.querySelector('.list-product__results-text');
    if (resultsText) {
        resultsText.textContent = `Showing products in color: ${color}`;
    }
}

/**
 * Update size filter UI
 */
function updateSizeFilterUI(selectedItem) {
    const size = selectedItem.textContent.trim();
    console.log('Size filter UI updated for:', size);

    // Remove active class from all size items
    const allSizeItems = document.querySelectorAll('.list-product__size-item');
    allSizeItems.forEach(item => {
        item.classList.remove('list-product__size-item--active');
    });

    // Add active class to selected item
    selectedItem.classList.add('list-product__size-item--active');

    // Update results text
    const resultsText = document.querySelector('.list-product__results-text');
    if (resultsText) {
        resultsText.textContent = `Showing products in size: ${size}`;
    }
}

/**
 * Update category filter UI
 */
function updateCategoryFilterUI(selectedLink) {
    const category = selectedLink.textContent.trim();
    console.log('Category filter UI updated for:', category);

    // Remove active class from all category links
    const allCategoryLinks = document.querySelectorAll('.list-product__category-link');
    allCategoryLinks.forEach(link => {
        link.classList.remove('list-product__category-link--active');
    });

    // Add active class to selected link
    selectedLink.classList.add('list-product__category-link--active');

    // Update results text
    const resultsText = document.querySelector('.list-product__results-text');
    if (resultsText) {
        resultsText.textContent = `Showing products in category: ${category}`;
    }
}

/**
 * Initialize filter interactions
 */
function initializeFilters() {
    try {
        // Color filter
        const colorInputs = document.querySelectorAll('.list-product__color-input');
        console.log('Found color inputs:', colorInputs.length);

        colorInputs.forEach((input, index) => {
            console.log(`Setting up color input ${index}:`, input.id, input.value);

            input.addEventListener('change', function () {
                try {
                    const color = this.value;
                    const colorId = this.id;
                    console.log('Color filter changed:', colorId, color);

                    // Add visual feedback
                    updateColorFilterUI(this);

                    // Add your color filtering logic here
                    filterProductsByColor(color);
                } catch (error) {
                    console.error('Error in color filter change:', error);
                }
            });

            // Also handle click events for better UX
            input.addEventListener('click', function () {
                console.log('Color input clicked:', this.id);
            });
        });
    } catch (error) {
        console.error('Error initializing color filters:', error);
    }

    // Size filter
    const sizeItems = document.querySelectorAll('.list-product__size-item');
    sizeItems.forEach(item => {
        item.addEventListener('click', function () {
            const size = this.textContent.trim();
            console.log('Size filter applied:', size);
            updateSizeFilterUI(this);
        });
    });

    // Category filter
    const categoryLinks = document.querySelectorAll('.list-product__category-link');
    categoryLinks.forEach(link => {
        link.addEventListener('click', function (e) {
            e.preventDefault();
            const category = this.textContent.trim();
            console.log('Category filter applied:', category);
            updateCategoryFilterUI(this);
        });
    });

    // Reset button
    const resetBtn = document.querySelector('.list-product__reset-btn');
    if (resetBtn) {
        resetBtn.addEventListener('click', function (e) {
            e.preventDefault();
            resetAllFilters();
        });
    }
}

/**
 * Reset all filters - UI only
 */
function resetAllFilters() {
    console.log('Resetting all filters...');

    // Reset price slider
    const slider = document.getElementById('price-slider');
    if (slider && slider.noUiSlider) {
        slider.noUiSlider.set([50, 366]);
    }

    // Reset color filters
    const colorInputs = document.querySelectorAll('.list-product__color-input');
    colorInputs.forEach(input => input.checked = false);

    // Remove active color options
    const activeColorOptions = document.querySelectorAll('.list-product__color-option--active');
    activeColorOptions.forEach(option => option.classList.remove('list-product__color-option--active'));

    // Reset size filters
    const sizeItems = document.querySelectorAll('.list-product__size-item');
    sizeItems.forEach(item => item.classList.remove('list-product__size-item--active'));

    // Set default active size (S)
    const defaultSizeItem = document.querySelector('.list-product__size-item');
    if (defaultSizeItem) {
        defaultSizeItem.classList.add('list-product__size-item--active');
    }

    // Reset category filters
    const activeCategoryLinks = document.querySelectorAll('.list-product__category-link--active');
    activeCategoryLinks.forEach(link => link.classList.remove('list-product__category-link--active'));

    // Remove active widget classes
    const activeWidgets = document.querySelectorAll('.list-product__widget--active');
    activeWidgets.forEach(widget => widget.classList.remove('list-product__widget--active'));

    // Reset results text
    const resultsText = document.querySelector('.list-product__results-text');
    if (resultsText) {
        resultsText.textContent = 'Showing all products';
    }

    console.log('All filters reset successfully');
}

/**
 * Initialize mobile sidebar functionality
 */
function initializeMobileSidebar() {
    const panelBtn = document.querySelector('.list-product__panel-btn');
    const sidebar = document.querySelector('.list-product__sidebar');
    const closeBtn = document.querySelector('.list-product__close-btn');

    if (panelBtn && sidebar) {
        panelBtn.addEventListener('click', function (e) {
            e.preventDefault();
            sidebar.classList.add('list-product__sidebar--open');
            document.body.classList.add('sidebar-open');
        });
    }

    if (closeBtn && sidebar) {
        closeBtn.addEventListener('click', function (e) {
            e.preventDefault();
            sidebar.classList.remove('list-product__sidebar--open');
            document.body.classList.remove('sidebar-open');
        });
    }

    // Close sidebar when clicking outside
    document.addEventListener('click', function (e) {
        if (sidebar && sidebar.classList.contains('list-product__sidebar--open')) {
            if (!sidebar.contains(e.target) && !panelBtn.contains(e.target)) {
                sidebar.classList.remove('list-product__sidebar--open');
                document.body.classList.remove('sidebar-open');
            }
        }
    });
}
