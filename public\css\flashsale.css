.flashsale{
  font-size: 0.652vw;
  padding: 6em 0;
}
.flashsale__wrapper{
  display: flex;
  flex-direction: column;
  border-radius: 1.5em;
  overflow: hidden;
  box-shadow: 0 0.4em 0.4em rgb(14 55 54 / 15%);
  background: #cd1818;
  background: linear-gradient(90deg, #c40000, #ff4d4d);
  padding: 2em 2em 2.5em;    
}
.flashsale__header{
  height: 9em;
  width: 100%;
  display: flex;
  justify-content: space-around;
}

.flashsale__countdown{
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  padding: 0 1.2em;
  font-size: 1.6em;
  color: rgb(var(--rgb-white));
  flex: 1; 
}
.flashsale__title{
  display: flex;
  align-items: center;
  margin-right: 8em;
  text-align: center;
  text-transform: uppercase;
  color: #fff;
  letter-spacing: 0.1em;
  text-shadow:
    0 0 0.5em #ffd700,
    0 0 1.5em #ff0000,
    0.2em 0.2em 0.6em rgba(0, 0, 0, 0.4);
}
.flashsale__title > h2{
  font-size: 6em;
  font-family: '<PERSON>', sans-serif;
}

.countdown__timer{
  margin-left: .4em;
  display: flex;
  align-items: center;
  gap: .2em;
}
.countdown__item{
  color: rgb(var(--rgb-black));
  background-color: rgb(var(--rgb-white));
  padding: 0.4em 0.2em;
  border-radius: 0.5em;
  margin: 0 0.2em;
  font-weight: 600;
  min-width: 2.5em;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}
.flashsale__content{
  padding: 1.8em;
  font-size: var(--vw-12);
}

.flashsale__footer{
  display: flex;
  justify-content: center;
  align-items: center;
}

.swiper-button-next.flashsale__next, .swiper-button-prev.flashsale__prev{
  width: 4em;
  height: 4em;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, .2);
  font-size: var(--vw-12);
}
.swiper-button-next.flashsale__next:after, .swiper-button-prev.flashsale__prev:after{
  font-size: 2em;
  color: rgb(var(--rgb-white));
}


