package components

import (
    "goweb/views/partials"
)


templ FlashsaleHome() {
<section class="flashsale">
    <div class="container90">
        <div class="flashsale__wrapper">
            <div class="flashsale__header">
                <div class="flashsale__countdown">
                    <div class="countdown__title"><h3>Kết thúc trong</h3></div>
                    <div class="countdown__timer" data-flashsale data-time-end="2025-09-26T15:00:00+07:00"></div>
                </div>
                <div class="flashsale__title">
                    <h2>FLASH SALE</h2>
                </div>
            </div>
            <div class="flashsale__content">
                <div class="swiper flashsale__swiper">
                    <div class="swiper-wrapper">
                        {{z:=1}}
                        for i:=1;i<=5; i++ {
                            @partials.ProductItem(" swiper-slide")
                            {{z = z+3}}
                        }
                    </div>

                    <div class="swiper-button-next flashsale__next"></div>
                    <div class="swiper-button-prev flashsale__prev"></div>
                </div>   
            </div>

            <div class="flashsale__footer">
                <a href="#" title="Xem chi tiết" class="btn-primary">Xem tất cả</a>
            </div>
            
        </div>
    </div>
</section>
}