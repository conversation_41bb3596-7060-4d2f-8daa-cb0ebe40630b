module goweb

go 1.24.1

replace github.com/networld-solution/sctx => ../sctx

replace github.com/networld-solution/gos => ../gos

require (
	github.com/a-h/templ v0.3.906
	github.com/gofiber/fiber/v2 v2.52.8
	github.com/networld-solution/gos v0.1.4
	github.com/networld-solution/sctx v0.1.8
	github.com/spf13/cobra v1.9.1
)

require (
	github.com/andybalholm/brotli v1.1.1 // indirect
	github.com/btcsuite/btcutil v1.0.2 // indirect
	github.com/bwmarrin/discordgo v0.29.0 // indirect
	github.com/facebookgo/flagenv v0.0.0-20160425205200-fcd59fca7456 // indirect
	github.com/gofiber/template v1.8.3 // indirect
	github.com/gofiber/template/html/v2 v2.1.3 // indirect
	github.com/gofiber/utils v1.1.0 // indirect
	github.com/golang-jwt/jwt/v5 v5.2.2 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/gorilla/websocket v1.4.2 // indirect
	github.com/inconshreveable/mousetrap v1.1.0 // indirect
	github.com/joho/godotenv v1.5.1 // indirect
	github.com/klauspost/compress v1.18.0 // indirect
	github.com/mattn/go-colorable v0.1.14 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/mattn/go-runewidth v0.0.16 // indirect
	github.com/montanaflynn/stats v0.7.1 // indirect
	github.com/namsral/flag v1.7.4-pre // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/rivo/uniseg v0.4.7 // indirect
	github.com/sirupsen/logrus v1.9.3 // indirect
	github.com/spf13/pflag v1.0.6 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasthttp v1.62.0 // indirect
	github.com/xdg-go/pbkdf2 v1.0.0 // indirect
	github.com/xdg-go/scram v1.1.2 // indirect
	github.com/xdg-go/stringprep v1.0.4 // indirect
	github.com/youmark/pkcs8 v0.0.0-20240726163527-a2c0da244d78 // indirect
	go.mongodb.org/mongo-driver v1.17.4 // indirect
	golang.org/x/crypto v0.39.0 // indirect
	golang.org/x/sync v0.15.0 // indirect
	golang.org/x/sys v0.33.0 // indirect
	golang.org/x/text v0.26.0 // indirect
	gopkg.in/natefinch/lumberjack.v2 v2.2.1 // indirect
)
