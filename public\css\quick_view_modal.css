.product-modal{
  position: fixed;
  inset: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  font-size: 0.652vw;
}

.product-modal--hidden{
  display: none;
}

.product-modal__overlay{
  position: absolute;
  inset: 0;
  background-color: rgba(var(--rgb-black), 0.6);
}

.product-modal__box{
  background: var(--white);
  position: relative;
  width: 60%;
  height: 52em;
  padding: 2.5em;
  z-index: 1;
}

.product-modal__close{
  position: absolute;
  top: .3em;
  right: 1em;
  font-size: 2em;
  border: none;
  background: none;
  cursor: pointer;
}

.product-modal__body{
  display: flex;
  align-items: center;
  justify-items: center;
  height: 100%;
  gap: 2.5em;
}

/* Hình ảnh */
.product-modal__gallery{
  width: 50%;
  height: 100%;
}

.product-modal__main-img{
  width: 100%;
  border-radius: 0.5em;
  object-fit: cover;
}

.product-modal__thumbs{
  display: flex;
  gap: 0.5em;
  margin-top: 1em;
}

.product-modal__thumbs > img{
  width: 3.75em;
  height: 3.75em;
  object-fit: cover;
  cursor: pointer;
  border: .1em solid var(--light-grey);
}

/* Nội dung */
.product-modal__content{
  width: 50%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.product-modal__content-top, .product-modal__content-bot{
  width: 100%;
  height: 100%;
}
.product-modal__content-box{
  display: flex;
  gap: 1em;
}

.product-modal__label{
  background: var(--black);
  color: var(--white);
  padding: .4em .8em;
  margin-bottom: 0.65em;
  font-size: 1.1em;
  border-radius: 1em;
  text-transform: uppercase;
  text-align: center;
  font-weight: 600;
}
.product-modal__label.product-modal__sale-percent{
  background-color: var(--primary);
}
.product-modal__label.product-modal__sale-hot{
  background-color: var(--light-dark);
}

.product-modal__title{
  margin: .3em 0 .3em;
  font-weight: 700;
}
.product-modal__title > a{
  font-size: 2.2em;
}

.product-modal__rating{
  margin-bottom: 0.8em;
  display: flex;
  align-items: center;
  gap: 1.5em;
}
.product-modal__rating small{
  font-size: 1.1em;
}
.product-modal__content > .rating-star{
  width: 6.55em;
}

.product-modal__desc{
  font-size: 1.3em;
  margin-bottom: .8em 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-modal__meta{
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 1.5em;
  flex-wrap: wrap;
}
.product-modal__meta-left{
  width: 40%;
}
.product-modal__meta-right{
  width: 60%;
}
.product-modal__meta-left, .product-modal__meta-right{
  height: 100%;
}
.product-modal__label-text{
  display: block;
  font-size: 1.3em;
  margin-bottom: 0.5em;
  font-weight: 700;
  color: var(--black);
  background: transparent;
  margin-bottom: .5em;
}
.product-modal__options.product__info{
  flex-direction:row;
  padding: 0;
  box-shadow: none;
}

.product-modal__price{
  font-size: 1.5em;
  font-weight: bold;
  margin-bottom: 0;
  color: var(--primary);
}

.product-modal__old-price{
  font-size: .75em;
  color: var(--light-grey);
  margin-left: 0.5em;
  text-decoration: line-through;
}

/* Quantity control */
.product-modal__quantity-control{
  display: flex;
  align-items: center;
}

.product-modal__quantity-inline{
  display: flex;
  align-items: center;
  gap: 0.5em;
}

.product-modal__quantity-input{
  padding: 0.5em;
  font-size: 0.652vw;
  text-align: center;
  border: .1em solid var(--black);
  width: 2.8em;
  height: 2.8em;
  border-radius: 50%;
}

.product-modal__btn{
  border: .1em solid var(--black);
  background-color: var(--black);
  color: var(--white);
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.product-modal__btn >.product-icon__down, 
.product-modal__btn >.product-icon__up{
  font-size: 0.652vw;
  width: 1.8em;
  height: 1.8em;
  fill: var(--white);
}
.product-modal__actions{
  margin: 1.2em 0;
  display: flex;
  align-items: center;
}
/* Nút */
.btn{
  padding: 0.75em 1.5em;
  cursor: pointer;
  border-radius: 0.7em;
  border: .1em solid var(--black);
}

.btn--primary > span, 
.btn--outline > span{
  font-size: 1.4em;
}

.btn--primary{
  position: relative;
  overflow: hidden;
  background: var(--black);
  display: flex;
  color: var(--white);
  margin-right: 1em;
  z-index: 1;
}
.btn--primary:hover{
  color: var(--white);
}
.btn--primary::after,
.btn--outline::after{
  content: "";
  position: absolute;
  top:0;
  left: 0;
  opacity: 0.8;
  visibility: hidden;
  height: 100%;
  width: 100%;
  transform: translateX(-105%);
  border-right: 0.2em solid var(--white);
  background-color: rgba(255, 255, 255, .5);
  transition: all 0.5s ease;
  pointer-events: none;
}
.btn--primary:hover::after,
.btn--outline:hover::after{
  transform: translate(0);
  opacity: 0;
  visibility: visible;
}

.btn--outline{
  position: relative;
  overflow: hidden;
  background: var(--white);
  color: var(--black);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1em;
}
.btn--outline:hover{
  background-color: var(--black);
  color: var(--white);
}
.btn--outline:hover >svg{
  fill: var(--white);
}

.product-modal__info{
  border-top: .1em solid rgba(var(--rgb-black), 0.2);
  padding-top: .8em;
  margin-bottom: .5em;
  display: flex;
  flex-direction: column;
}
.product-modal__info >p{
  margin: 0.3em 0;
  font-size: 1.2em;
}
.product-modal__info >p>strong{
  font-size: 1.1em;
}
.product-modal__social-icon{
  width: 100%;
}
.product-modal__social-icon >ul{
  display: flex;
  align-items: center;
  gap: 1.5em;
}
.product-modal__social-icon .product-icon__social-item{
  width: 2em;
  height: 2em;
}
.product-modal__options{
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.product-modal__options-left{
  position: relative;
  width: 40%;
}
.product-modal__options-left >.product-size{
  flex-direction: row;
  width: 100%;
  justify-content: flex-start;
  left: 0;
  bottom: 0;
  opacity: 1;
}
.product-modal__options-right{
  width: 60%;
}
.product-viewer{
  width: 50% !important;
  height: 100% !important;
  position: relative;
}
.swiper-quick-view{
  width: 100%;
  height: 100%;
}
.product-viewer >.product-thumbnails{
  position: absolute;
  top: 2em;
  left: 2em;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 1em;
}
.product-viewer .product-thumbnails >img{
  border: .1em solid var(--white);
  object-fit: cover;
  width: 5em;
  height: 6em;
  cursor: pointer;
  border-radius: .36em;
  transition: 0.3s;
}
.product-viewer .product-thumbnails img.active{
  border-color: var(--primary);
}
.product__box.product__style,
.product__box .product__thumb-img{
  height: 100%;
}
.product__box .product__thumb-img >img{
  object-fit: cover;
  object-position: center;
  width: 100%;
  height: 100%;
}