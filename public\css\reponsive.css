/* 
* <576:     xs
* 576-768:  sm
* 768-992:  md
* 992-1200: lg
* 1200 -1400: xl
* >= 1400:  xxl
*/
@media (max-width: 575px){
    .xs-hidden{
        display: none;
    }
    .xs-block{
        display: block;
    }
    .xs-news-w100{
        width: 100%;
    }
    .xs-box-w100{
        max-width: 100%;
    }       

}
@media (min-width: 576px) and (max-width: 768px){
  
}

@media (max-width: 768px){ 
    :root{
        --10px: 1.5vw;
    }

    .container90{
        width: 100%;
        padding-left: 1rem;
        padding-right: 1rem;
    }
    .header-info-bar, .cate-menus__wrapper, .menus-nav, .header-right{
        display: none;
    }
    .maybi__logo, .menu-mobile-bar{
        display: block;
    }
    .header-navbars{
        height: 5em;       
    }

    .voucher__wrapper {
        grid-template-columns: repeat(2, 1fr);  
        gap: 1em;
    }

    .vouchers{
        padding-top: 3em;
    }

    .categories {      
        grid-template-columns: repeat(5, 20%);
        gap: 2em;
    }
    .category__thumb{
        width: 6em;
        height: 6em;
    }
    .category__icon {
        width: 5em;
        height: 4em;
    }

    .product-list {        
        grid-template-columns: repeat(2, 1fr);
    }

}

@media only screen and (min-width: 769px) and (max-width: 1024px) {
  
}

@media only screen and (min-width: 992px) and (max-width: 1199px){
   
    
}

@media only screen and (min-width: 1200px) and (max-width: 1399px){
    
}